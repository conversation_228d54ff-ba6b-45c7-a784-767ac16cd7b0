#include "AudioManager.h"
#include <WiFi.h>
#include <HTTPClient.h>
#include <WiFiClient.h>

// 默认音频URL（示例）
const char* defaultAudioURLs[] = {
    "",  // 欢迎语音URL（空，需要通过MQTT设置）
    "",  // 感谢语音URL（空，需要通过MQTT设置）
    ""   // 错误提示音URL（空，需要通过MQTT设置）
};

// 音频文件定义
const char* audioNames[] = {
    "welcome",      // 欢迎语音
    "thank_you",    // 感谢语音
    "error"         // 错误提示音
};

const char* audioNvsKeys[] = {
    "audio_url_welcome",    // 欢迎语音URL NVS键
    "audio_url_thankyou",   // 感谢语音URL NVS键
    "audio_url_error"       // 错误提示音URL NVS键
};

AudioManager::AudioManager()
    : _volume(100),      // 默认音量80%
      _initialized(false),
      _playing(false),
      _currentAudio(0)
{
    // 初始化音频文件结构
    for (int i = 0; i < AUDIO_COUNT; i++) {
        _audioFiles[i].name = audioNames[i];
        _audioFiles[i].nvs_key = audioNvsKeys[i];
        _audioFiles[i].url = defaultAudioURLs[i];
        _audioFiles[i].localPath = generateLocalPath(i);
        _audioFiles[i].size = 0;
        _audioFiles[i].loaded = false;
        _audioFiles[i].mode = AUDIO_MODE_URL;  // 默认使用URL模式
    }
}

AudioManager::~AudioManager()
{
    // 停止音频播放
    if (_initialized) {
        _audio.stopSong();
    }

    // 关闭NVS
    _preferences.end();
}

bool AudioManager::begin()
{
    // 初始化LittleFS文件系统
    if (!initFileSystem()) {
        Serial.println("文件系统初始化失败");
        return false;
    }

    // 初始化Audio库和I2S
    if (!initAudio()) {
        Serial.println("Audio库初始化失败");
        return false;
    }

    // 打开NVS
    if (!_preferences.begin("audio", false)) {
        Serial.println("NVS初始化失败");
        return false;
    }

    // 加载音频URL和检查本地文件
    if (!loadAudioData()) {
        Serial.println("音频数据加载失败，使用默认设置");
        initDefaultAudio();
    }

    _initialized = true;
    Serial.println("音频管理器初始化成功");
    return true;
}

bool AudioManager::initAudio()
{
    // 设置I2S引脚 - 适用于MAX98357A
    _audio.setPinout(I2S_BCLK_PIN, I2S_LRCLK_PIN, I2S_DOUT_PIN);

    // 设置音量 (0-21)
    _audio.setVolume(_volume * 21 / 100);  // 将0-100%转换为0-21

    Serial.println("Audio库初始化成功 - MAX98357A I2S");
    return true;
}

void AudioManager::initDefaultAudio()
{
    // 初始化默认音频设置
    for (int i = 0; i < AUDIO_COUNT; i++) {
        _audioFiles[i].url = defaultAudioURLs[i];
        _audioFiles[i].loaded = false;
        _audioFiles[i].mode = AUDIO_MODE_URL;

        // 检查是否有本地文件
        if (localAudioExists(i)) {
            _audioFiles[i].mode = AUDIO_MODE_LOCAL;
            _audioFiles[i].loaded = true;
            _audioFiles[i].size = getLocalAudioSize(i);
            Serial.printf("发现本地音频文件: %s (%d字节)\n", _audioFiles[i].name, _audioFiles[i].size);
        }

        // 保存URL到NVS（如果有的话）
        if (strlen(defaultAudioURLs[i]) > 0) {
            saveAudioToNVS(i, defaultAudioURLs[i]);
            if (_audioFiles[i].mode == AUDIO_MODE_URL) {
                _audioFiles[i].loaded = true;
            }
        }
    }

    Serial.println("初始化默认音频设置完成");
}

bool AudioManager::loadAudioData()
{
    bool allLoaded = true;

    // 尝试从NVS加载所有音频URL并检查本地文件
    for (int i = 0; i < AUDIO_COUNT; i++) {
        // 先检查本地文件
        if (localAudioExists(i)) {
            _audioFiles[i].mode = AUDIO_MODE_LOCAL;
            _audioFiles[i].loaded = true;
            _audioFiles[i].size = getLocalAudioSize(i);
            Serial.printf("加载本地音频文件: %s (%d字节)\n", _audioFiles[i].name, _audioFiles[i].size);
        } else {
            // 如果没有本地文件，尝试加载URL
            if (!loadAudioFromNVS(i)) {
                allLoaded = false;
            } else {
                _audioFiles[i].mode = AUDIO_MODE_URL;
            }
        }
    }

    return allLoaded;
}

bool AudioManager::loadAudioFromNVS(uint8_t index)
{
    if (index >= AUDIO_COUNT) {
        return false;
    }

    // 从NVS读取音频URL
    String url = _preferences.getString(_audioFiles[index].nvs_key, "");
    if (url.length() == 0) {
        Serial.printf("NVS中不存在音频URL: %s\n", _audioFiles[index].name);
        return false;
    }

    _audioFiles[index].url = url;
    _audioFiles[index].loaded = true;
    _audioFiles[index].mode = AUDIO_MODE_URL;

    Serial.printf("从NVS加载音频URL成功: %s -> %s\n", _audioFiles[index].name, url.c_str());
    return true;
}

bool AudioManager::saveAudioToNVS(uint8_t index, const String& url)
{
    if (index >= AUDIO_COUNT || url.length() == 0) {
        return false;
    }

    // 保存URL到NVS
    size_t written = _preferences.putString(_audioFiles[index].nvs_key, url);
    if (written == 0) {
        Serial.printf("NVS写入失败: %s -> %s\n", _audioFiles[index].name, url.c_str());
        return false;
    }

    Serial.printf("保存音频URL到NVS成功: %s -> %s\n", _audioFiles[index].name, url.c_str());
    return true;
}

bool AudioManager::playWelcome()
{
    return playAudio(AUDIO_WELCOME);
}

bool AudioManager::playAudio(uint8_t index)
{
    if (!_initialized || index >= AUDIO_COUNT) {
        Serial.printf("播放音频失败: 未初始化或索引无效 (%d)\n", index);
        return false;
    }

    if (!_audioFiles[index].loaded) {
        Serial.printf("播放音频失败: 音频未加载 (%s)\n", _audioFiles[index].name);
        return false;
    }

    // 停止当前播放
    stopPlay();

    bool success = false;

    // 根据播放模式选择播放方式
    if (_audioFiles[index].mode == AUDIO_MODE_LOCAL) {
        // 播放本地文件
        success = playLocalAudio(index);
    } else if (_audioFiles[index].mode == AUDIO_MODE_URL) {
        // 播放网络音频
        if (_audioFiles[index].url.length() == 0) {
            Serial.printf("播放音频失败: 音频URL未设置 (%s)\n", _audioFiles[index].name);
            return false;
        }
        success = _audio.connecttohost(_audioFiles[index].url.c_str());
        if (success) {
            Serial.printf("开始播放网络音频: %s -> %s\n", _audioFiles[index].name, _audioFiles[index].url.c_str());
        } else {
            Serial.printf("播放音频失败: 无法连接到URL (%s)\n", _audioFiles[index].url.c_str());
        }
    }

    if (success) {
        _currentAudio = index;
        _playing = true;
    }

    return success;
}

bool AudioManager::stopPlay()
{
    if (!_initialized) {
        return false;
    }

    // 停止音频播放
    _audio.stopSong();
    _playing = false;

    Serial.println("停止播放音频");
    return true;
}

bool AudioManager::setVolume(uint8_t volume)
{
    if (volume > 100) {
        volume = 100;
    }

    _volume = volume;

    // 将0-100%转换为0-21（ESP32-audioI2S库的音量范围）
    uint8_t audioVolume = volume * 21 / 100;
    _audio.setVolume(audioVolume);

    Serial.printf("设置音量: %d%% (Audio库音量: %d)\n", volume, audioVolume);
    return true;
}

uint8_t AudioManager::getVolume() const
{
    return _volume;
}

void AudioManager::handleEvents()
{
    // 如果没有初始化，直接返回
    if (!_initialized) {
        return;
    }

    // 处理Audio库的事件循环
    _audio.loop();

    // 检查播放状态
    if (_playing && !_audio.isRunning()) {
        _playing = false;
        Serial.printf("音频播放完成: %s\n", _audioFiles[_currentAudio].name);
    }
}

// 新增函数：保存音频URL
bool AudioManager::saveAudioURL(uint8_t index, const String& url)
{
    if (index >= AUDIO_COUNT) {
        return false;
    }

    // 更新内存中的URL
    _audioFiles[index].url = url;
    _audioFiles[index].loaded = (url.length() > 0);
    _audioFiles[index].mode = AUDIO_MODE_URL;

    // 保存到NVS
    return saveAudioToNVS(index, url);
}

// 新增函数：从URL播放音频
bool AudioManager::playAudioFromURL(uint8_t index, const String& url)
{
    if (index >= AUDIO_COUNT) {
        return false;
    }

    // 先保存URL
    if (!saveAudioURL(index, url)) {
        return false;
    }

    // 然后播放
    return playAudio(index);
}

// 新增函数：检查是否正在播放
bool AudioManager::isPlaying()
{
    return _playing && _audio.isRunning();
}

// 初始化LittleFS文件系统
bool AudioManager::initFileSystem()
{
    if (!LittleFS.begin(true)) {
        Serial.println("LittleFS初始化失败，尝试格式化...");
        if (!LittleFS.format()) {
            Serial.println("LittleFS格式化失败");
            return false;
        }
        if (!LittleFS.begin(true)) {
            Serial.println("LittleFS格式化后仍然无法挂载");
            return false;
        }
        Serial.println("LittleFS格式化并挂载成功");
    }

    // 创建音频目录
    if (!LittleFS.exists("/audio")) {
        if (LittleFS.mkdir("/audio")) {
            Serial.println("创建音频目录成功: /audio");
        } else {
            Serial.println("创建音频目录失败: /audio");
        }
    }

    Serial.println("LittleFS文件系统初始化成功");
    return true;
}

// 生成本地音频文件路径
String AudioManager::generateLocalPath(uint8_t index)
{
    if (index >= AUDIO_COUNT) {
        return "";
    }
    return "/audio/" + String(audioNames[index]) + ".mp3";
}

// 下载音频文件到本地存储
bool AudioManager::downloadAudioFile(uint8_t index, const String& url)
{
    if (index >= AUDIO_COUNT || url.length() == 0) {
        Serial.println("下载音频文件失败: 参数无效");
        return false;
    }

    String localPath = generateLocalPath(index);
    Serial.printf("开始下载音频文件: %s -> %s\n", url.c_str(), localPath.c_str());

    // 下载文件
    if (!downloadFileInternal(url, localPath)) {
        Serial.printf("下载音频文件失败: %s\n", _audioFiles[index].name);
        return false;
    }

    // 更新音频文件信息
    _audioFiles[index].localPath = localPath;
    _audioFiles[index].mode = AUDIO_MODE_LOCAL;
    _audioFiles[index].loaded = true;
    _audioFiles[index].size = getLocalAudioSize(index);

    Serial.printf("下载音频文件成功: %s (%d字节)\n", _audioFiles[index].name, _audioFiles[index].size);
    return true;
}

// 从本地文件播放音频
bool AudioManager::playLocalAudio(uint8_t index)
{
    if (index >= AUDIO_COUNT) {
        return false;
    }

    if (!localAudioExists(index)) {
        Serial.printf("本地音频文件不存在: %s\n", _audioFiles[index].name);
        return false;
    }

    String localPath = _audioFiles[index].localPath;
    bool success = _audio.connecttoFS(LittleFS, localPath.c_str());

    if (success) {
        Serial.printf("开始播放本地音频: %s -> %s\n", _audioFiles[index].name, localPath.c_str());
    } else {
        Serial.printf("播放本地音频失败: %s\n", localPath.c_str());
    }

    return success;
}

// 删除本地音频文件
bool AudioManager::deleteLocalAudio(uint8_t index)
{
    if (index >= AUDIO_COUNT) {
        return false;
    }

    String localPath = generateLocalPath(index);
    if (LittleFS.exists(localPath)) {
        if (LittleFS.remove(localPath)) {
            Serial.printf("删除本地音频文件成功: %s\n", localPath.c_str());

            // 更新音频文件信息
            _audioFiles[index].mode = AUDIO_MODE_URL;
            _audioFiles[index].size = 0;
            if (_audioFiles[index].url.length() == 0) {
                _audioFiles[index].loaded = false;
            }

            return true;
        } else {
            Serial.printf("删除本地音频文件失败: %s\n", localPath.c_str());
            return false;
        }
    }

    return true; // 文件不存在也算成功
}

// 检查本地音频文件是否存在
bool AudioManager::localAudioExists(uint8_t index)
{
    if (index >= AUDIO_COUNT) {
        return false;
    }

    String localPath = generateLocalPath(index);
    return LittleFS.exists(localPath);
}

// 获取本地音频文件大小
size_t AudioManager::getLocalAudioSize(uint8_t index)
{
    if (index >= AUDIO_COUNT) {
        return 0;
    }

    String localPath = generateLocalPath(index);
    if (LittleFS.exists(localPath)) {
        File file = LittleFS.open(localPath, "r");
        if (file) {
            size_t size = file.size();
            file.close();
            return size;
        }
    }

    return 0;
}

// 下载文件的内部实现
bool AudioManager::downloadFileInternal(const String& url, const String& localPath)
{
    HTTPClient http;
    WiFiClient client;

    // 设置超时时间
    http.setTimeout(30000); // 30秒超时

    // 开始HTTP请求
    http.begin(client, url);

    int httpCode = http.GET();

    if (httpCode != HTTP_CODE_OK) {
        Serial.printf("HTTP请求失败，响应码: %d\n", httpCode);
        http.end();
        return false;
    }

    // 获取文件大小
    int contentLength = http.getSize();
    Serial.printf("文件大小: %d 字节\n", contentLength);

    // 创建本地文件
    File file = LittleFS.open(localPath, "w");
    if (!file) {
        Serial.printf("无法创建本地文件: %s\n", localPath.c_str());
        http.end();
        return false;
    }

    // 获取数据流
    WiFiClient* stream = http.getStreamPtr();

    // 下载数据
    uint8_t buffer[1024];
    int totalBytes = 0;

    while (http.connected() && (contentLength > 0 || contentLength == -1)) {
        size_t availableBytes = stream->available();

        if (availableBytes) {
            int bytesToRead = min(availableBytes, sizeof(buffer));
            int bytesRead = stream->readBytes(buffer, bytesToRead);

            if (bytesRead > 0) {
                file.write(buffer, bytesRead);
                totalBytes += bytesRead;

                if (contentLength > 0) {
                    contentLength -= bytesRead;
                }

                // 显示下载进度
                if (totalBytes % 10240 == 0) { // 每10KB显示一次
                    Serial.printf("已下载: %d 字节\n", totalBytes);
                }
            }
        }

        delay(1); // 避免看门狗重置
    }

    file.close();
    http.end();

    Serial.printf("文件下载完成: %d 字节\n", totalBytes);
    return totalBytes > 0;
}
