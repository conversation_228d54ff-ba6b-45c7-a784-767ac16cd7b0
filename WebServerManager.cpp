#include "WebServerManager.h"
#include <ArduinoJson.h>
#include <LittleFS.h>
#include <Update.h>
#include <HTTPClient.h>
#include <WiFiClient.h>
#include "config.h"
#include "SystemCore.h"
#include "Air780E.h"

WebServerManager::WebServerManager(HardwareNetworkManager *networkManager, MQTTClient *mqttClient, DeviceControl *deviceControl, OTAManager *otaManager)
    : _hardwareNetworkManager(networkManager),
      _mqttClient(mqttClient),
      _deviceControl(deviceControl),
      _otaManager(otaManager),
      _air780e(nullptr),
      _server(80)
{

    // 在begin方法中获取Air780E实例

    // 初始化配置
    memset(&_config, 0, sizeof(_config));
    memset(&_status, 0, sizeof(_status));

    // 设置默认配置
    strncpy(_config.device_name, DEVICE_NAME, sizeof(_config.device_name) - 1);
    _config.network_mode = NETWORK_MODE_WIFI;
    _config.wifi_enabled = true;
    _config.cellular_enabled = true;
    strncpy(_config.mqtt_server, DEFAULT_MQTT_SERVER, sizeof(_config.mqtt_server) - 1);
    _config.mqtt_port = DEFAULT_MQTT_PORT;
    strncpy(_config.mqtt_user, DEFAULT_MQTT_USER, sizeof(_config.mqtt_user) - 1);
    strncpy(_config.mqtt_password, DEFAULT_MQTT_PASSWORD, sizeof(_config.mqtt_password) - 1);
    strncpy(_config.mqtt_client_id, DEFAULT_MQTT_CLIENT_ID, sizeof(_config.mqtt_client_id) - 1);
    strncpy(_config.mqtt_topic_status, DEFAULT_MQTT_TOPIC_STATUS, sizeof(_config.mqtt_topic_status) - 1);
    strncpy(_config.mqtt_topic_command, DEFAULT_MQTT_TOPIC_COMMAND, sizeof(_config.mqtt_topic_command) - 1);
    strncpy(_config.api_goodsOut, DEFAULT_API_GOODS_OUT, sizeof(_config.api_goodsOut) - 1);
    strncpy(_config.api_cabinetBinTest, DEFAULT_API_CABINET_BIN_TEST, sizeof(_config.api_cabinetBinTest) - 1);
    // strncpy(_config.api_getDeviceParams, DEFAULT_API_GET_DEVICE_PARAMS, sizeof(_config.api_getDeviceParams) - 1);
    _config.mqtt_update_interval = 60; // 默认60秒

    // 设置默认AP模式SSID和密码
    strncpy(_config.web_server_ssid, DEFAULT_AP_SSID, sizeof(_config.web_server_ssid) - 1);
    strncpy(_config.web_server_password, DEFAULT_AP_PASSWORD, sizeof(_config.web_server_password) - 1);
}

bool WebServerManager::begin()
{
    // 获取Air780E实例
    _air780e = SystemCore::getInstance()->getAir780EPtr();
    if (_air780e == nullptr)
    {
        Serial.println("无法获取Air780E实例");
    }

    // 初始化LittleFS
    if (!LittleFS.begin(true))
    {
        Serial.println("LittleFS初始化失败，尝试格式化...");

        if (!LittleFS.format())
        {
            Serial.println("LittleFS格式化失败");
            return false;
        }

        if (!LittleFS.begin(true))
        {
            Serial.println("LittleFS格式化后仍然无法挂载");
            return false;
        }
        Serial.println("LittleFS格式化并挂载成功");
    }

    // 加载配置
    loadConfig();

    // 文件列表
    listFiles();

    return true;
}

bool WebServerManager::start()
{
    // 启动Web服务器
    Serial.println("正在启动Web服务器...");
    Serial.printf("  当前WiFi模式: %d\n", WiFi.getMode());
    Serial.printf("  AP活跃: %s\n", WiFi.softAPgetStationNum() > 0 ? "是" : "否");
    Serial.printf("  AP IP地址: %s\n", WiFi.softAPIP().toString().c_str());

    // 使用类成员变量跟踪服务器状态，避免重复启动
    if (!_serverRunning)
    {
        // 设置路由
        setupRoutes();

        // 仅在服务器未运行时启动
        _server.begin();
        _serverRunning = true;
        Serial.println("Web服务器已启动，监听端口: 80");
    }
    else
    {
        Serial.println("Web服务器已经在运行，不需要重新启动");
    }

    return true;
}

bool WebServerManager::stop()
{
    // 停止Web服务器
    if (_serverRunning)
    {
        _server.close();
        _serverRunning = false;
        Serial.println("Web服务器已停止");
    }
    return true;
}

void WebServerManager::handleEvents()
{
    // 处理客户端请求
    if (_serverRunning)
    {
        _server.handleClient();
    }
}

void WebServerManager::updateStatus(const DeviceStatus &status)
{
    // 更新状态
    _status = status;
}

void WebServerManager::setupRoutes()
{
    Serial.println("设置Web服务器路由...");

    // 设置根路径处理
    _server.on("/", HTTP_GET, std::bind(&WebServerManager::handleRoot, this));

    // 处理网络连接检测请求(Android系统使用类似/generate_204的请求来检测网络连接状态和是否需要登录门户)
    _server.on("/generate_204", HTTP_GET, [this]()
               {
        Serial.println("收到网络连接检测请求: /generate_204");
        _server.send(204, "text/plain", ""); });

    // 处理网络连接检测请求(带UUID的原因：后面的随机字符串（如UUID）是为了防止缓存，确保每次请求都真正到达服务器而不是从缓存获取响应)
    // _server.on("/generate_204_*", HTTP_GET, [this]() {
    //     Serial.printf("收到带UUID的网络连接检测请求: %s\n", _server.uri().c_str());
    //     _server.send(204, "text/plain", "");
    // });

    // 处理mmtls请求(移动设备或应用程序的网络连接检测请求。与腾讯的MMTLS协议有关，这是一种用于微信等应用的安全传输层协议。这类请求通常是自动生成的，用于检测网络连接状态或进行网络环境探测。)
    // _server.on("/mmtls/", HTTP_GET, [this](){
    //     Serial.printf("收到mmtls连接检测请求:: %s\n", _server.uri().c_str());
    //     _server.send(204, "text/plain", "");
    // });

    // API路由
    // 添加简单的API测试路由
    _server.on("/api/ping", HTTP_GET, [this]()
               {
        Serial.println("收到ping请求");
        _server.send(200, "application/json", "{\"status\":\"ok\",\"message\":\"pong\"}"); });

    // 获取配置
    _server.on("/api/config", HTTP_GET, std::bind(&WebServerManager::handleGetConfig, this));

    // 设置配置
    _server.on("/api/config", HTTP_POST, std::bind(&WebServerManager::handleSetConfig, this));

    // 获取状态
    _server.on("/api/status", HTTP_GET, std::bind(&WebServerManager::handleGetStatus, this));

    // 控制电机
    // _server.on("/api/motor", HTTP_POST, std::bind(&WebServerManager::handleControlMotor, this));

    // 出货
    _server.on("/api/dispense", HTTP_POST, std::bind(&WebServerManager::handleDispenseProduct, this));

    // 控制继电器
    _server.on("/api/relay", HTTP_POST, std::bind(&WebServerManager::handleRelayControl, this));

    // 重启系统
    _server.on("/api/restart", HTTP_POST, std::bind(&WebServerManager::handleRestart, this));

    // OTA升级
    _server.on("/api/update", HTTP_POST, std::bind(&WebServerManager::handleOTAUpload, this));

    // 静态文件处理
    _server.onNotFound([this]()
                       {
        if (!handleFileRead(_server.uri())) {
            handleNotFound();
        } });

    Serial.println("Web服务器路由设置完成");
}

void WebServerManager::handleRoot()
{
    Serial.println("收到根路径请求");
    if (LittleFS.exists("/index.html"))
    {
        handleFileRead("/index.html");
    }
    else
    {
        // 如果没有index.html，返回一个简单的页面
        String html = "<!DOCTYPE html><html><head><title>测试页面</title></head><body>";
        html += "<h1>自助贩卖机控制系统</h1>";
        html += "<p>Web服务器工作正常！</p>";
        html += "<p>当前时间: " + String(millis() / 1000) + "秒</p>";
        html += "</body></html>";
        _server.send(200, "text/html", html);
    }
}

void WebServerManager::handleNotFound()
{
    String uri = _server.uri();

    Serial.printf("未找到路径: %s\n", uri.c_str());
    _server.send(404, "text/plain", "页面未找到");
}

String WebServerManager::getContentType(String filename)
{
    if (filename.endsWith(".html"))
        return "text/html";
    else if (filename.endsWith(".css"))
        return "text/css";
    else if (filename.endsWith(".js"))
        return "application/javascript";
    else if (filename.endsWith(".json"))
        return "application/json";
    else if (filename.endsWith(".png"))
        return "image/png";
    else if (filename.endsWith(".jpg"))
        return "image/jpeg";
    else if (filename.endsWith(".ico"))
        return "image/x-icon";
    return "text/plain";
}

bool WebServerManager::handleFileRead(String path)
{
    if (path.endsWith("/"))
        path += "index.html";
    String contentType = getContentType(path);

    if (LittleFS.exists(path))
    {
        File file = LittleFS.open(path, "r");
        _server.streamFile(file, contentType);
        file.close();
        Serial.printf("请求文件成功: %s\n", path.c_str());
        return true;
    }

    return false;
}

void WebServerManager::handleGetConfig()
{
    // 创建JSON文档
    StaticJsonDocument<1024> jsonDoc;

    // 添加配置信息
    jsonDoc["device_name"] = _config.device_name;
    jsonDoc["network_mode"] = _config.network_mode;

    // WiFi配置
    JsonObject wifi = jsonDoc.createNestedObject("wifi");
    wifi["enabled"] = _config.wifi_enabled;
    wifi["ssid"] = _config.wifi_ssid;
    wifi["password"] = _config.wifi_password; // 返回真实密码

    // 4G配置
    JsonObject cellular = jsonDoc.createNestedObject("cellular");
    cellular["enabled"] = _config.cellular_enabled;
    cellular["apn"] = _config.apn;
    cellular["user"] = _config.apn_user;
    cellular["password"] = _config.apn_password; // 返回真实密码

    // MQTT配置
    JsonObject mqtt = jsonDoc.createNestedObject("mqtt");
    mqtt["server"] = _config.mqtt_server;
    mqtt["port"] = _config.mqtt_port;
    mqtt["user"] = _config.mqtt_user;
    mqtt["password"] = _config.mqtt_password; // 返回真实密码
    mqtt["client_id"] = _config.mqtt_client_id;
    mqtt["topic_status"] = _config.mqtt_topic_status;
    mqtt["topic_command"] = _config.mqtt_topic_command;
    mqtt["update_interval"] = _config.mqtt_update_interval;

    // Web服务器配置
    JsonObject web_server = jsonDoc.createNestedObject("web_server");
    web_server["ssid"] = _config.web_server_ssid;
    web_server["password"] = _config.web_server_password; // 返回真实密码

    // API接口配置
    JsonObject api = jsonDoc.createNestedObject("api");
    api["goodsOut"] = _config.api_goodsOut;
    api["cabinetBinTest"] = _config.api_cabinetBinTest;
    // api["getDeviceParams"] = _config.api_getDeviceParams;

    // 系统信息
    JsonObject system = jsonDoc.createNestedObject("system");
    system["firmware"] = FIRMWARE_VERSION;
    system["uptime"] = millis() / 1000;

    // 序列化JSON
    String jsonResponse;
    serializeJson(jsonDoc, jsonResponse);

    // 发送响应
    _server.send(200, "application/json", jsonResponse);
}

void WebServerManager::handleSetConfig()
{
    // 检查是否有请求体
    if (!_server.hasArg("plain"))
    {
        _server.send(400, "application/json", "{\"success\":false,\"message\":\"No request body\"}");
        return;
    }

    // 获取请求体
    String body = _server.arg("plain");

    // 解析JSON
    StaticJsonDocument<1024> jsonDoc;
    DeserializationError error = deserializeJson(jsonDoc, body);

    if (error)
    {
        _server.send(400, "application/json", "{\"success\":false,\"message\":\"Invalid JSON\"}");
        return;
    }

    // 临时配置
    SystemConfig newConfig = _config;

    // 更新配置
    if (jsonDoc.containsKey("device_name"))
    {
        strncpy(newConfig.device_name, jsonDoc["device_name"], sizeof(newConfig.device_name) - 1);
    }

    if (jsonDoc.containsKey("network_mode"))
    {
        newConfig.network_mode = jsonDoc["network_mode"];
    }

    // WiFi配置
    if (jsonDoc.containsKey("wifi"))
    {
        JsonObject wifi = jsonDoc["wifi"];

        if (wifi.containsKey("enabled"))
        {
            newConfig.wifi_enabled = wifi["enabled"];
        }

        if (wifi.containsKey("ssid"))
        {
            strncpy(newConfig.wifi_ssid, wifi["ssid"], sizeof(newConfig.wifi_ssid) - 1);
        }

        if (wifi.containsKey("password"))
        {
            strncpy(newConfig.wifi_password, wifi["password"], sizeof(newConfig.wifi_password) - 1);
        }
    }

    // 4G配置
    if (jsonDoc.containsKey("cellular"))
    {
        JsonObject cellular = jsonDoc["cellular"];

        if (cellular.containsKey("enabled"))
        {
            newConfig.cellular_enabled = cellular["enabled"];
        }

        if (cellular.containsKey("apn"))
        {
            strncpy(newConfig.apn, cellular["apn"], sizeof(newConfig.apn) - 1);
        }

        if (cellular.containsKey("user"))
        {
            strncpy(newConfig.apn_user, cellular["user"], sizeof(newConfig.apn_user) - 1);
        }

        if (cellular.containsKey("password"))
        {
            strncpy(newConfig.apn_password, cellular["password"], sizeof(newConfig.apn_password) - 1);
        }
    }

    // Web服务器配置
    if (jsonDoc.containsKey("web_server"))
    {
        JsonObject web_server = jsonDoc["web_server"];

        if (web_server.containsKey("ssid"))
        {
            strncpy(newConfig.web_server_ssid, web_server["ssid"], sizeof(newConfig.web_server_ssid) - 1);
        }

        if (web_server.containsKey("password"))
        {
            strncpy(newConfig.web_server_password, web_server["password"], sizeof(newConfig.web_server_password) - 1);
        }
    }

    // MQTT配置
    if (jsonDoc.containsKey("mqtt"))
    {
        JsonObject mqtt = jsonDoc["mqtt"];

        if (mqtt.containsKey("server"))
        {
            strncpy(newConfig.mqtt_server, mqtt["server"], sizeof(newConfig.mqtt_server) - 1);
        }

        if (mqtt.containsKey("port"))
        {
            newConfig.mqtt_port = mqtt["port"];
        }

        if (mqtt.containsKey("user"))
        {
            strncpy(newConfig.mqtt_user, mqtt["user"], sizeof(newConfig.mqtt_user) - 1);
        }

        if (mqtt.containsKey("password"))
        {
            strncpy(newConfig.mqtt_password, mqtt["password"], sizeof(newConfig.mqtt_password) - 1);
        }

        if (mqtt.containsKey("client_id"))
        {
            strncpy(newConfig.mqtt_client_id, mqtt["client_id"], sizeof(newConfig.mqtt_client_id) - 1);
        }

        if (mqtt.containsKey("topic_status"))
        {
            strncpy(newConfig.mqtt_topic_status, mqtt["topic_status"], sizeof(newConfig.mqtt_topic_status) - 1);
        }

        if (mqtt.containsKey("topic_command"))
        {
            strncpy(newConfig.mqtt_topic_command, mqtt["topic_command"], sizeof(newConfig.mqtt_topic_command) - 1);
        }

        if (mqtt.containsKey("update_interval"))
        {
            newConfig.mqtt_update_interval = mqtt["update_interval"];
        }
    }

    // API接口配置
    if (jsonDoc.containsKey("api"))
    {
        JsonObject api = jsonDoc["api"];

        if (api.containsKey("goodsOut"))
        {
            strncpy(newConfig.api_goodsOut, api["goodsOut"], sizeof(newConfig.api_goodsOut) - 1);
        }

        if (api.containsKey("cabinetBinTest"))
        {
            strncpy(newConfig.api_cabinetBinTest, api["cabinetBinTest"], sizeof(newConfig.api_cabinetBinTest) - 1);
        }

        // if (api.containsKey("getDeviceParams"))
        // {
        //     strncpy(newConfig.api_getDeviceParams, api["getDeviceParams"], sizeof(newConfig.api_getDeviceParams) - 1);
        // }
    }

    // 确保web_server_ssid和web_server_password不为空
    if (strlen(newConfig.web_server_ssid) == 0) {
        Serial.println("警告: Web服务器SSID为空，使用默认值");
        strncpy(newConfig.web_server_ssid, DEFAULT_AP_SSID, sizeof(newConfig.web_server_ssid) - 1);
    }

    if (strlen(newConfig.web_server_password) == 0) {
        Serial.println("警告: Web服务器密码为空，使用默认值");
        strncpy(newConfig.web_server_password, DEFAULT_AP_PASSWORD, sizeof(newConfig.web_server_password) - 1);
    }

    // 更新配置
    _config = newConfig;

    // 保存配置
    saveConfig();

    // 应用配置
    applyConfig();

    // 发送响应
    _server.send(200, "application/json", "{\"success\":true}");
}

void WebServerManager::handleGetStatus()
{
    // 创建JSON文档
    StaticJsonDocument<1024> jsonDoc;

    // 获取最新的MQTT连接状态
    bool mqttConnected = _mqttClient->isConnected();

    // 添加状态信息
    jsonDoc["wifi_connected"] = _status.wifi_connected;
    jsonDoc["cellular_connected"] = _status.cellular_connected;
    jsonDoc["mqtt_connected"] = mqttConnected; // 使用最新的MQTT连接状态
    jsonDoc["motor_running"] = _status.motor_running;

    // 添加所有红外传感器状态
    jsonDoc["ir_triggered"] = _status.ir_triggered;      // 红外传感器1
    jsonDoc["ir_triggered2"] = _status.ir_triggered2;    // 红外传感器2
    jsonDoc["ir_triggered3"] = _status.ir_triggered3;    // 红外传感器3
    jsonDoc["pir_triggered"] = _status.pir_triggered;    // 人体红外传感器

    // 添加所有继电器状态
    JsonArray relayStatus = jsonDoc.createNestedArray("relay_status");
    for (int i = 0; i < 5; i++) {  // 5个继电器
        relayStatus.add(_status.relay_status[i]);
    }

    // 添加设备通信状态和设备状态
    jsonDoc["device_communication_error"] = _status.device_communication_error;
    jsonDoc["device_status"] = _status.device_status;

    // 获取最新的运行时间（毫秒转换为秒）
    uint32_t currentUptime = millis() / 1000;
    jsonDoc["uptime"] = currentUptime; // 使用最新的运行时间

    // 获取最新的信号强度
    uint8_t signalStrength = _hardwareNetworkManager->getSignalStrength();
    jsonDoc["signal_strength"] = signalStrength; // 使用最新的信号强度

    // 添加位置信息
    if (_status.latitude != 0 || _status.longitude != 0)
    {
        jsonDoc["latitude"] = _status.latitude;
        jsonDoc["longitude"] = _status.longitude;
        jsonDoc["location_update"] = _status.last_location_update;
    }

    // 添加网络信息
    jsonDoc["network_mode"] = _hardwareNetworkManager->getNetworkMode() == NETWORK_MODE_WIFI ? "WiFi" : "4G";
    jsonDoc["ip_address"] = _hardwareNetworkManager->getIPAddress();

    // 添加系统信息
    jsonDoc["firmware"] = FIRMWARE_VERSION;
    jsonDoc["free_heap"] = ESP.getFreeHeap();
    jsonDoc["mac_address"] = _status.mac_address;

    // 添加可用空间信息（用于固件升级）
    jsonDoc["sketch_space"] = ESP.getFreeSketchSpace();

    // 序列化JSON
    String jsonResponse;
    serializeJson(jsonDoc, jsonResponse);

    // 发送响应
    _server.send(200, "application/json", jsonResponse);
}

void WebServerManager::handleControlMotor()
{
    // 检查是否有请求体
    if (!_server.hasArg("plain"))
    {
        _server.send(400, "application/json", "{\"success\":false,\"message\":\"No request body\"}");
        return;
    }

    // 获取请求体
    String body = _server.arg("plain");

    // 解析JSON
    StaticJsonDocument<256> jsonDoc;
    DeserializationError error = deserializeJson(jsonDoc, body);

    if (error)
    {
        _server.send(400, "application/json", "{\"success\":false,\"message\":\"Invalid JSON\"}");
        return;
    }

    // 获取参数
    uint32_t channelNumber = jsonDoc.containsKey("address") ? jsonDoc["address"] : DEFAULT_CHANNEL_NUMBER;

    // 电机控制命令已经被移除，改为使用出货命令
    bool result = _deviceControl->dispenseProduct(channelNumber);

    // 创建JSON响应
    StaticJsonDocument<256> responseDoc;
    responseDoc["success"] = result;
    responseDoc["message"] = "Motor control command is deprecated, using dispense command instead";
    responseDoc["channel"] = channelNumber;

    // 序列化JSON
    String jsonResponse;
    serializeJson(responseDoc, jsonResponse);

    // 发送响应
    _server.send(200, "application/json", jsonResponse);
}

void WebServerManager::handleDispenseProduct()
{
    // 检查是否有请求体
    if (!_server.hasArg("plain"))
    {
        _server.send(400, "application/json", "{\"success\":false,\"message\":\"No request body\"}");
        return;
    }

    // 获取请求体
    String body = _server.arg("plain");

    // 解析JSON
    StaticJsonDocument<256> jsonDoc;
    DeserializationError error = deserializeJson(jsonDoc, body);

    if (error)
    {
        _server.send(400, "application/json", "{\"success\":false,\"message\":\"Invalid JSON\"}");
        return;
    }

    // 获取行列参数
    uint32_t rowNumber = jsonDoc.containsKey("row") ? jsonDoc["row"] : 1;
    uint32_t columnNumber = jsonDoc.containsKey("column") ? jsonDoc["column"] : 1;

    // 兼容旧版本的地址参数
    uint32_t channelNumber = jsonDoc.containsKey("address") ? jsonDoc["address"] : rowNumber;

    Serial.printf("出货请求: 行=%d, 列=%d\n", rowNumber, columnNumber);

    bool result = false;

    // 检查是否是多通道出货
    if (jsonDoc.containsKey("quantity"))
    {
        // 暂不支持多通道出货测试
        _server.send(400, "application/json", "{\"success\":false,\"message\":\"Multi-channel dispensing not supported\"}");
    }
    else
    {
        // 单通道出货
        result = _deviceControl->dispenseProduct(channelNumber, columnNumber);

        // 延时一段时间等待货物掉落
        delay(1000);

        // 创建JSON响应
        StaticJsonDocument<256> responseDoc;

        // 添加结果信息
        responseDoc["command"] = "dispense_result";
        responseDoc["success"] = result;
        responseDoc["ir_triggered"] = _deviceControl->checkIRSensor();
        responseDoc["row"] = rowNumber;
        responseDoc["column"] = columnNumber;
        responseDoc["multi_channel"] = false;
        responseDoc["device_id"] = _hardwareNetworkManager->getUniqueDeviceId();
        responseDoc["device_name"] = DEVICE_NAME;
        responseDoc["timestamp"] = millis();

        // 序列化JSON
        String jsonResponse;
        serializeJson(responseDoc, jsonResponse);

        // 发送响应
        _server.send(200, "application/json", jsonResponse);
    }
}

void WebServerManager::handleRestart()
{
    // 发送响应
    _server.send(200, "text/plain", "系统将在3秒后重启");

    // 延迟重启
    delay(3000);
    ESP.restart();
}

void WebServerManager::handleRelayControl()
{
    // 检查是否有请求体
    if (!_server.hasArg("plain"))
    {
        _server.send(400, "application/json", "{\"success\":false,\"message\":\"No request body\"}");
        return;
    }

    // 获取请求体
    String body = _server.arg("plain");

    // 解析JSON
    StaticJsonDocument<256> jsonDoc;
    DeserializationError error = deserializeJson(jsonDoc, body);

    if (error)
    {
        _server.send(400, "application/json", "{\"success\":false,\"message\":\"Invalid JSON\"}");
        return;
    }

    bool success = false;

    // 检查是否是控制所有继电器
    if (jsonDoc.containsKey("all_relays") && jsonDoc["all_relays"].as<bool>())
    {
        // 控制所有继电器
        bool state = jsonDoc["state"];

        // 调用设备控制接口控制所有继电器
        success = _deviceControl->setAllRelays(state);

        // 创建JSON响应
        StaticJsonDocument<256> responseDoc;
        responseDoc["success"] = success;
        responseDoc["message"] = state ? "所有继电器已开启" : "所有继电器已关闭";

        // 序列化JSON
        String jsonResponse;
        serializeJson(responseDoc, jsonResponse);

        // 发送响应
        _server.send(200, "application/json", jsonResponse);
    }
    else if (jsonDoc.containsKey("relay_number") && jsonDoc.containsKey("state"))
    {
        // 控制单个继电器
        uint8_t relayNumber = jsonDoc["relay_number"];
        bool state = jsonDoc["state"];

        // 调用设备控制接口控制继电器
        success = _deviceControl->setRelay(relayNumber, state);

        // 创建JSON响应
        StaticJsonDocument<256> responseDoc;
        responseDoc["success"] = success;
        responseDoc["message"] = state ? "继电器已开启" : "继电器已关闭";
        responseDoc["relay_number"] = relayNumber;

        // 序列化JSON
        String jsonResponse;
        serializeJson(responseDoc, jsonResponse);

        // 发送响应
        _server.send(200, "application/json", jsonResponse);
    }
    else
    {
        // 参数错误
        _server.send(400, "application/json", "{\"success\":false,\"message\":\"Missing required parameters\"}");
    }
}

void WebServerManager::handleOTAUpload()
{
    HTTPUpload &upload = _server.upload();

    if (upload.status == UPLOAD_FILE_START)
    {
        Serial.printf("开始OTA升级: %s\n", upload.filename.c_str());

        // 检查文件名是否以.bin结尾
        if (!upload.filename.endsWith(".bin")) {
            Serial.println("错误: 文件不是.bin格式");
            _server.send(400, "text/plain", "错误: 请上传.bin格式的固件文件");
            return;
        }

        // 获取可用空间
        size_t freeSpace = ESP.getFreeSketchSpace();
        Serial.printf("可用空间: %u 字节\n", freeSpace);

        // 设置分区类型为应用程序
        if (!Update.begin(UPDATE_SIZE_UNKNOWN, U_FLASH))
        {
            String errorMsg = "OTA初始化失败: ";
            Update.printError(Serial);
            switch (Update.getError()) {
                case UPDATE_ERROR_SPACE:
                    errorMsg += "空间不足";
                    break;
                case UPDATE_ERROR_SIZE:
                    errorMsg += "大小错误";
                    break;
                case UPDATE_ERROR_WRITE:
                    errorMsg += "写入错误";
                    break;
                case UPDATE_ERROR_ERASE:
                    errorMsg += "擦除错误";
                    break;
                case UPDATE_ERROR_READ:
                    errorMsg += "读取错误";
                    break;
                default:
                    errorMsg += "未知错误";
            }
            _server.send(500, "text/plain", errorMsg);
            return;
        }

        Serial.println("OTA升级准备就绪");
    }
    else if (upload.status == UPLOAD_FILE_WRITE)
    {
        // 写入数据
        Serial.printf("正在写入 %d 字节数据\n", upload.currentSize);

        if (Update.write(upload.buf, upload.currentSize) != upload.currentSize)
        {
            String errorMsg = "OTA写入失败: ";
            int error = Update.getError();
            Serial.printf("OTA写入错误代码: %d\n", error);
            Update.printError(Serial);

            switch (error) {
                case UPDATE_ERROR_WRITE:
                    errorMsg += "写入错误";
                    break;
                case UPDATE_ERROR_SPACE:
                    errorMsg += "空间不足";
                    break;
                case UPDATE_ERROR_SIZE:
                    errorMsg += "大小错误";
                    break;
                case UPDATE_ERROR_ERASE:
                    errorMsg += "擦除错误";
                    break;
                case UPDATE_ERROR_BAD_ARGUMENT:
                    errorMsg += "参数错误";
                    break;
                default:
                    errorMsg += "未知错误";
            }
            Serial.println(errorMsg);
            _server.send(500, "text/plain", errorMsg);
            return;
        }

        // 打印进度
        static int lastProgress = 0;
        int progress = 0;

        // 避免除以零错误
        if (Update.size() > 0) {
            progress = (Update.progress() * 100) / Update.size();
        }

        // 每10%打印一次进度
        if (progress != lastProgress && (progress % 10 == 0 || progress == 100)) {
            lastProgress = progress;
            Serial.printf("OTA升级进度: %d%%, 已写入: %u 字节, 总大小: %u 字节\n",
                         progress, Update.progress(), Update.size());
        }
    }
    else if (upload.status == UPLOAD_FILE_END)
    {
        Serial.printf("OTA升级完成，准备验证固件，接收了 %u 字节\n", upload.totalSize);

        // 检查接收到的数据大小
        if (upload.totalSize == 0) {
            Serial.println("错误: 接收到的固件大小为0");
            _server.send(500, "text/plain", "升级失败: 接收到的固件大小为0");
            return;
        }

        // 完成升级
        bool updateSuccess = Update.end(true);
        int error = Update.getError();

        // 打印错误代码和信息
        Serial.printf("OTA错误代码: %d\n", error);
        Update.printError(Serial);

        // 特殊情况：有时候即使Update.end()返回false，但错误代码为0，实际上升级是成功的
        if (updateSuccess || (error == 0))
        {
            Serial.printf("OTA升级成功，接收了 %u 字节\n", upload.totalSize);
            _server.send(200, "text/plain", "升级成功，系统将重启");
            delay(1000);
            ESP.restart();
        }
        else
        {
            String errorMsg = "OTA完成失败: ";

            switch (error) {
                case UPDATE_ERROR_SPACE:
                    errorMsg += "空间不足";
                    break;
                case UPDATE_ERROR_SIZE:
                    errorMsg += "大小错误";
                    break;
                case UPDATE_ERROR_WRITE:
                    errorMsg += "写入错误";
                    break;
                case UPDATE_ERROR_ERASE:
                    errorMsg += "擦除错误";
                    break;
                case UPDATE_ERROR_READ:
                    errorMsg += "读取错误";
                    break;
                case UPDATE_ERROR_MD5:
                    errorMsg += "MD5校验失败";
                    break;
                case UPDATE_ERROR_MAGIC_BYTE:
                    errorMsg += "固件格式错误";
                    break;
                case UPDATE_ERROR_ACTIVATE:
                    errorMsg += "激活新固件失败";
                    break;
                case UPDATE_ERROR_NO_PARTITION:
                    errorMsg += "找不到分区";
                    break;
                case UPDATE_ERROR_BAD_ARGUMENT:
                    errorMsg += "参数错误";
                    break;
                case UPDATE_ERROR_ABORT:
                    errorMsg += "升级已中止";
                    break;
                default:
                    errorMsg += "未知错误";
            }
            Serial.println(errorMsg);
            _server.send(500, "text/plain", errorMsg);
        }
    }
    else if (upload.status == UPLOAD_FILE_ABORTED)
    {
        Update.abort();
        Serial.println("OTA升级已取消");
        _server.send(400, "text/plain", "升级已取消");
    }
    else
    {
        _server.send(500, "text/plain", "升级失败: 未知错误");
    }
}

bool WebServerManager::saveConfig()
{
    // 创建JSON文档
    StaticJsonDocument<1024> jsonDoc;

    // 添加配置信息
    jsonDoc["device_name"] = _config.device_name;
    jsonDoc["network_mode"] = _config.network_mode;

    // WiFi配置
    JsonObject wifi = jsonDoc.createNestedObject("wifi");
    wifi["enabled"] = _config.wifi_enabled;
    wifi["ssid"] = _config.wifi_ssid;
    wifi["password"] = _config.wifi_password;

    // 4G配置
    JsonObject cellular = jsonDoc.createNestedObject("cellular");
    cellular["enabled"] = _config.cellular_enabled;
    cellular["apn"] = _config.apn;
    cellular["user"] = _config.apn_user;
    cellular["password"] = _config.apn_password;

    // MQTT配置
    JsonObject mqtt = jsonDoc.createNestedObject("mqtt");
    mqtt["server"] = _config.mqtt_server;
    mqtt["port"] = _config.mqtt_port;
    mqtt["user"] = _config.mqtt_user;
    mqtt["password"] = _config.mqtt_password;
    mqtt["client_id"] = _config.mqtt_client_id;
    mqtt["topic_status"] = _config.mqtt_topic_status;
    mqtt["topic_command"] = _config.mqtt_topic_command;
    mqtt["update_interval"] = _config.mqtt_update_interval;

    // Web服务器配置
    JsonObject web_server = jsonDoc.createNestedObject("web_server");
    web_server["ssid"] = _config.web_server_ssid;
    web_server["password"] = _config.web_server_password;

    // 打开文件
    File file = LittleFS.open("/config.json", FILE_WRITE);
    if (!file)
    {
        Serial.println("无法打开配置文件");
        return false;
    }

    // 序列化JSON到文件
    if (serializeJson(jsonDoc, file) == 0)
    {
        Serial.println("写入配置文件失败");
        file.close();
        return false;
    }

    // 关闭文件
    file.close();
    return true;
}

bool WebServerManager::loadConfig()
{
    // 检查文件是否存在
    if (!LittleFS.exists("/config.json"))
    {
        Serial.println("配置文件不存在，使用默认配置并创建配置文件");
        // 保存默认配置到文件
        if (saveConfig())
        {
            Serial.println("默认配置已保存到文件");
            return true;
        }
        else
        {
            Serial.println("保存默认配置失败");
            return false;
        }
    }

    // 打开文件
    File file = LittleFS.open("/config.json", FILE_READ);
    if (!file)
    {
        Serial.println("无法打开配置文件");
        return false;
    }

    // 创建JSON文档
    StaticJsonDocument<1024> jsonDoc;

    // 解析JSON
    DeserializationError error = deserializeJson(jsonDoc, file);
    file.close();

    // 检查解析错误
    if (error)
    {
        Serial.printf("解析配置文件失败: %s\n", error.c_str());
        return false;
    }

    // 更新配置
    if (jsonDoc.containsKey("device_name"))
    {
        strncpy(_config.device_name, jsonDoc["device_name"], sizeof(_config.device_name) - 1);
    }

    if (jsonDoc.containsKey("network_mode"))
    {
        _config.network_mode = jsonDoc["network_mode"];
    }

    // WiFi配置
    if (jsonDoc.containsKey("wifi"))
    {
        JsonObject wifi = jsonDoc["wifi"];

        if (wifi.containsKey("enabled"))
        {
            _config.wifi_enabled = wifi["enabled"];
        }

        if (wifi.containsKey("ssid"))
        {
            strncpy(_config.wifi_ssid, wifi["ssid"], sizeof(_config.wifi_ssid) - 1);
        }
        else
        {
            Serial.println("配置文件中未找到WiFi SSID");
        }

        if (wifi.containsKey("password"))
        {
            strncpy(_config.wifi_password, wifi["password"], sizeof(_config.wifi_password) - 1);
        }
        else
        {
            // 确保密码为空
            memset(_config.wifi_password, 0, sizeof(_config.wifi_password));
            Serial.println("配置文件中未找到WiFi密码");
        }
    }

    // 4G配置
    if (jsonDoc.containsKey("cellular"))
    {
        JsonObject cellular = jsonDoc["cellular"];

        if (cellular.containsKey("enabled"))
        {
            _config.cellular_enabled = cellular["enabled"];
        }

        if (cellular.containsKey("apn"))
        {
            strncpy(_config.apn, cellular["apn"], sizeof(_config.apn) - 1);
        }

        if (cellular.containsKey("user"))
        {
            strncpy(_config.apn_user, cellular["user"], sizeof(_config.apn_user) - 1);
        }

        if (cellular.containsKey("password"))
        {
            strncpy(_config.apn_password, cellular["password"], sizeof(_config.apn_password) - 1);
        }
    }

    // MQTT配置
    if (jsonDoc.containsKey("mqtt"))
    {
        JsonObject mqtt = jsonDoc["mqtt"];

        if (mqtt.containsKey("server"))
        {
            strncpy(_config.mqtt_server, mqtt["server"], sizeof(_config.mqtt_server) - 1);
        }

        if (mqtt.containsKey("port"))
        {
            _config.mqtt_port = mqtt["port"];
        }

        if (mqtt.containsKey("user"))
        {
            strncpy(_config.mqtt_user, mqtt["user"], sizeof(_config.mqtt_user) - 1);
        }

        if (mqtt.containsKey("password"))
        {
            strncpy(_config.mqtt_password, mqtt["password"], sizeof(_config.mqtt_password) - 1);
        }

        if (mqtt.containsKey("client_id"))
        {
            strncpy(_config.mqtt_client_id, mqtt["client_id"], sizeof(_config.mqtt_client_id) - 1);
        }

        if (mqtt.containsKey("topic_status"))
        {
            strncpy(_config.mqtt_topic_status, mqtt["topic_status"], sizeof(_config.mqtt_topic_status) - 1);
        }

        if (mqtt.containsKey("topic_command"))
        {
            strncpy(_config.mqtt_topic_command, mqtt["topic_command"], sizeof(_config.mqtt_topic_command) - 1);
        }

        if (mqtt.containsKey("update_interval"))
        {
            _config.mqtt_update_interval = mqtt["update_interval"];
        }
    }

    // Web服务器配置
    if (jsonDoc.containsKey("web_server"))
    {
        JsonObject web_server = jsonDoc["web_server"];

        if (web_server.containsKey("ssid"))
        {
            strncpy(_config.web_server_ssid, web_server["ssid"], sizeof(_config.web_server_ssid) - 1);
        }

        if (web_server.containsKey("password"))
        {
            strncpy(_config.web_server_password, web_server["password"], sizeof(_config.web_server_password) - 1);
        }
    }

    // 确保web_server_ssid和web_server_password不为空
    if (strlen(_config.web_server_ssid) == 0) {
        Serial.println("警告: 配置文件中Web服务器SSID为空，使用默认值");
        strncpy(_config.web_server_ssid, DEFAULT_AP_SSID, sizeof(_config.web_server_ssid) - 1);
    }

    if (strlen(_config.web_server_password) == 0) {
        Serial.println("警告: 配置文件中Web服务器密码为空，使用默认值");
        strncpy(_config.web_server_password, DEFAULT_AP_PASSWORD, sizeof(_config.web_server_password) - 1);
    }

    return true;
}

bool WebServerManager::applyConfig()
{
    // 打印配置信息
    Serial.println("应用配置:");
    Serial.printf("  网络模式: %s\n", _config.network_mode == NETWORK_MODE_WIFI ? "WiFi" : "4G");
    Serial.printf("  WiFi SSID: '%s'\n", _config.wifi_ssid);
    Serial.printf("  WiFi密码长度: %d\n", strlen(_config.wifi_password));

    // 配置网络
    _hardwareNetworkManager->configureWiFi(_config.wifi_ssid, _config.wifi_password);
    _hardwareNetworkManager->configure4G(_config.apn, _config.apn_user, _config.apn_password);
    _hardwareNetworkManager->setNetworkMode(_config.network_mode);

    // 获取设备唯一ID
    String deviceId = _hardwareNetworkManager->getUniqueDeviceId();

    // 始终使用设备唯一ID作为客户端ID，忽略配置中的客户端ID
    strncpy(_config.mqtt_client_id, deviceId.c_str(), sizeof(_config.mqtt_client_id) - 1);
    Serial.printf("MQTT客户端ID已设置为设备唯一ID: %s\n", _config.mqtt_client_id);

    // 配置MQTT
    _mqttClient->configure(_config.mqtt_server, _config.mqtt_port, _config.mqtt_user, _config.mqtt_password, _config.mqtt_client_id);

    // 使用设备名称和MAC地址组合作为主题标识符
    String deviceName = String(_config.device_name);
    if (deviceName.isEmpty()) {
        deviceName = DEVICE_NAME; // 使用默认设备名称
    }

    // 构建状态主题: 设备名称/DEFAULT_MQTT_TOPIC_STATUS/设备ID
    String statusTopic = deviceName + "/" + String(DEFAULT_MQTT_TOPIC_STATUS) + "/" + deviceId + "/cabinet";
    strncpy(_config.mqtt_topic_status, statusTopic.c_str(), sizeof(_config.mqtt_topic_status) - 1);
    Serial.printf("MQTT状态主题设置为: %s\n", _config.mqtt_topic_status);

    // 构建命令主题: 设备名称/DEFAULT_MQTT_TOPIC_COMMAND/设备ID
    String commandTopic = deviceName + "/" + String(DEFAULT_MQTT_TOPIC_COMMAND) + "/" + deviceId;
    strncpy(_config.mqtt_topic_command, commandTopic.c_str(), sizeof(_config.mqtt_topic_command) - 1);
    Serial.printf("MQTT命令主题设置为: %s\n", _config.mqtt_topic_command);

    // 不再需要位置主题，所有消息通过状态主题发送
    _mqttClient->setTopics(_config.mqtt_topic_status, _config.mqtt_topic_command);

    return true;
}

void WebServerManager::listFiles()
{
    Serial.println("文件列表:");
    File root = LittleFS.open("/");
    File file = root.openNextFile();
    while (file)
    {
        Serial.printf("  %s, 大小: %d字节\n", file.name(), file.size());
        file = root.openNextFile();
    }

    Serial.println("文件列表结束");
}

bool WebServerManager::hasClients()
{
    // 检查是否有客户端连接到AP
    return WiFi.softAPgetStationNum() > 0;
}

bool WebServerManager::sendDispenseResultViaAPI(JsonDocument &resultDoc)
{
    // 序列化JSON
    String resultPayload;
    serializeJson(resultDoc, resultPayload);

    // 检查JSON格式
    StaticJsonDocument<512> checkDoc;
    DeserializationError error = deserializeJson(checkDoc, resultPayload);
    if (error)
    {
        Serial.print("结果JSON格式错误: ");
        Serial.println(error.c_str());
        return false;
    }

    // API服务器地址
    String apiServer = "http://xmc.xmtxrj.com:8082/apiSaasWrgj/memberOrderGoodsOut/updateMemberOrderGoodsOutStatus_barCode";

    // 检查当前网络模式
    int networkMode = _hardwareNetworkManager->getNetworkMode();
    bool success = false;

    if (_hardwareNetworkManager->isConnected())
    {
        if (networkMode == NETWORK_MODE_WIFI)
        {
            // 使用WIFI发送HTTP请求
            Serial.println("使用wifi发送HTTP请求...");

            // 创建HTTP客户端
            WiFiClient client;
            HTTPClient http;

            // 设置超时时间
            http.setTimeout(10000); // 10秒超时

            // 开始HTTP请求
            http.begin(client, apiServer);
            http.addHeader("Content-Type", "application/json");

            // 发送POST请求
            Serial.println("通过wifi API发送出货结果...");
            Serial.print("API地址: ");
            Serial.println(apiServer);
            Serial.print("发送数据: ");
            Serial.println(resultPayload);

            int httpCode = http.POST(resultPayload);

            // 检查HTTP响应
            if (httpCode > 0)
            {
                Serial.printf("HTTP响应码: %d\n", httpCode);

                if (httpCode == HTTP_CODE_OK)
                {
                    String response = http.getString();
                    Serial.println("服务器响应: " + response);
                    success = true;
                }
            }
            else
            {
                Serial.printf("HTTP请求失败，错误: %s\n", http.errorToString(httpCode).c_str());
            }

            http.end();
        }
        else if (networkMode == NETWORK_MODE_4G)
        {
            // 使用4G模块发送HTTP请求
            Serial.println("使用4G模块发送HTTP请求...");

            // 提取URL中的主机名和路径
            String url = apiServer;

            // 发送POST请求
            Serial.println("通过4G API发送出货结果...");
            Serial.print("API地址: ");
            Serial.println(url);
            Serial.print("发送数据: ");
            Serial.println(resultPayload);

            // 获取Air780E实例
            Air780E *air780e = SystemCore::getInstance()->getAir780EPtr();

            if (air780e != nullptr)
            {
                // 使用4G模块发送HTTP POST请求
                String response = air780e->httpPost(url.c_str(), "application/json", resultPayload.c_str(), 10000);

                if (response.length() > 0)
                {
                    Serial.println("4G HTTP请求成功");
                    Serial.print("服务器响应: ");
                    Serial.println(response);
                    success = true;
                }
                else
                {
                    Serial.println("4G HTTP请求失败");
                }
            }
            else
            {
                Serial.println("无法获取Air780E实例");
            }
        }
    }
    else
    {
        Serial.println("无法发送HTTP请求: WiFi和4G都未连接");
    }

    return success;
}
