#ifndef STATUS_MANAGER_H
#define STATUS_MANAGER_H

#include <Arduino.h>
#include "config.h"

// 前向声明
class MQTTClient;
class DeviceControl;
class Air780E;
class HardwareNetworkManager;
class ConnectionManager;
class WebServerManager;

class StatusManager {
public:
    StatusManager();

    // 初始化状态管理器
    bool begin(MQTTClient* mqttClient, DeviceControl* deviceControl, Air780E* air780e,
               HardwareNetworkManager* networkManager, ConnectionManager* connectionManager,
               WebServerManager* webServer);

    // 更新设备状态
    void updateDeviceStatus();

    // 处理状态相关事件
    void handleEvents();

    // 获取设备状态（const版本）
    const DeviceStatus& getDeviceStatus() const { return _deviceStatus; }

    // 获取设备状态（非const版本）
    DeviceStatus& getDeviceStatus() { return _deviceStatus; }

    // 发布参数
    bool publishParams(const char* topicCode);

    // 发布状态信息
    bool publishStatus(const char* topicCode);

    // 发布位置信息
    bool publishLocation(const char* topicCode);

    // 获取位置信息
    bool updateLocation();

private:
    MQTTClient* _mqttClient;
    DeviceControl* _deviceControl;
    Air780E* _air780e;
    HardwareNetworkManager* _hardwareNetworkManager;
    ConnectionManager* _connectionManager;
    WebServerManager* _webServer;

    // 设备状态
    DeviceStatus _deviceStatus;

    // 上次位置更新时间
    unsigned long _lastLocationUpdate;

    // 上次状态发布时间
    unsigned long _lastStatusPublish;

    // 状态更新间隔（毫秒）
    const unsigned long STATUS_UPDATE_INTERVAL = 30000;  // 30秒

    // 位置更新间隔（毫秒）
    const unsigned long LOCATION_UPDATE_INTERVAL = 1800000;  // 30分钟
};

#endif // STATUS_MANAGER_H
