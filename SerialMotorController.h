#ifndef SERIAL_MOTOR_CONTROLLER_H
#define SERIAL_MOTOR_CONTROLLER_H

#include <Arduino.h>
#include <HardwareSerial.h>
#include "config.h"

// 原协议通信常量
#define FRAME_HEADER 0xAA
#define FRAME_FOOTER 0xAB

// 原协议命令码
#define CMD_QUERY_DEVICE 0x00
#define CMD_QUERY_RESPONSE 0x80
#define CMD_DISPENSE_SINGLE 0x34
#define CMD_DISPENSE_SINGLE_RESPONSE 0xB4
#define CMD_DISPENSE_MULTI 0x35
#define CMD_DISPENSE_MULTI_RESPONSE 0xB5

// XP协议通信常量
#define XP_FRAME_HEADER1 0x58     // 'X'
#define XP_FRAME_HEADER2 0x50     // 'P'
#define XP_FRAME_FOOTER  0x4C     // 'L'

// XP协议命令码
#define XP_CMD_HANDSHAKE 0x00     // 握手命令
#define XP_CMD_DISPENSE  0x11     // 出货命令
#define XP_CMD_DISPENSE_ERROR 0x10 // 出货错误
#define XP_CMD_COIN_SUCCESS 0x21  // 投币成功
#define XP_CMD_REFUND 0x31        // 退币命令
#define XP_CMD_REFUND_SUCCESS 0x31 // 退币成功
#define XP_CMD_REFUND_FAILED 0x30 // 退币失败
#define XP_CMD_BILL_INSERT 0x40   // 纸币投入
#define XP_CMD_BILL_ERROR 0x41    // 纸币机错误
#define XP_CMD_BACK_KEY 0x50      // 后台键按下
#define XP_CMD_RESET 0xAA         // 全部马达复位
// #define XP_CMD_IDLE 0x00          // 闲置反馈

// 出货状态
#define DISPENSE_STATUS_COMPLETE 0x00
#define DISPENSE_STATUS_IN_PROGRESS 0x01
#define DISPENSE_STATUS_FAILED 0x02

// 通信接口类型
enum SerialInterfaceType
{
    INTERFACE_SZ1_RS485 = 1, // 深圳对接第一个厂家RS485接口
    INTERFACE_SZ1_RS232 = 2  // 深圳对接第一个厂家RS232接口
};

class SerialMotorController {
public:
    SerialMotorController();

    // 初始化控制器
    bool begin(SerialInterfaceType interfaceType = INTERFACE_SZ1_RS232);

    // 设置通信接口类型
    void setInterfaceType(SerialInterfaceType type);

    // 查询设备状态
    bool queryDevice(bool isNetworkConnected);

    // 单通道出货
    bool dispenseSingleChannel(uint32_t channelNumber, uint32_t ColumnNumber);

    // 多通道出货
    bool dispenseMultiChannel(uint32_t channelNumber, uint32_t ColumnNumber, uint16_t anotherChannelNumber, uint32_t anothercolumnNumber, uint16_t quantity);

    // 处理接收到的数据
    void handleReceivedData();

    // 获取最后一次出货状态
    uint8_t getLastDispenseStatus();

    // 获取是否允许扫码支付（作为设备正常工作的标志）
    bool getAllowScanStatus();

    // 获取设备通信状态（是否异常）
    bool isDeviceCommunicationError();

    // 重置设备通信异常状态
    void resetDeviceCommunicationError();

    // 处理事件
    void handleEvents();

    // 获取通信接口类型
    SerialInterfaceType getInterfaceType();

private:
    HardwareSerial* _serial;
    SerialInterfaceType _interfaceType;
    uint8_t _lastDispenseStatus;
    uint32_t _lastQueryTime;
    uint8_t _receiveBuffer[64];
    uint8_t _receiveIndex;
    bool _frameStarted;
    uint32_t _currentChannelNumber;
    uint32_t _currentColumnNumber;
    uint32_t _anotherChannelNumber;
    uint32_t _anotherColumnNumber;
    uint16_t _currentQuantity;
    bool _isMultiChannel;
    bool _waitingForResponse;
    uint32_t _lastCommandTime;
    uint32_t _responseTimeout;
    uint8_t _queryCount;         // 查询次数
    uint8_t _maxQueryCount;      // 最大查询次数
    bool _allowScan;             // 是否允许扫码支付（设备正常工作的标志）
    uint8_t _queryTimeoutCount;  // 查询超时计数
    bool _deviceCommunicationError; // 设备通信异常标志
    unsigned long _lastQueryResponseTime; // 最后一次查询响应时间

    // 发送查询命令并等待响应
    bool sendQueryCommand(bool isNetworkConnected, uint32_t timeout = 1000);

    // 处理查询响应
    void processQueryResponse(uint8_t* data, uint8_t length);

    // 发送单通道出货命令并等待响应
    bool sendSingleDispenseCommand(uint32_t channelNumber, uint32_t ColumnNumber, uint32_t timeout = 1000);

    // 处理单通道出货响应
    void processSingleDispenseResponse(uint8_t* data, uint8_t length);

    // 发送多通道出货命令并等待响应
    bool sendMultiDispenseCommand(uint32_t channelNumber, uint32_t ColumnNumber, uint16_t quantity, uint32_t timeout = 1000);

    // 处理多通道出货响应
    void processMultiDispenseResponse(uint8_t* data, uint8_t length);

    // 等待响应
    bool waitForResponse(uint32_t timeout);

    // 计算校验和
    void calculateChecksum(uint8_t* data, uint8_t length, uint8_t* checksum);

    // 验证校验和
    bool verifyChecksum(uint8_t* data, uint8_t length);

    // 清空接收缓冲区
    void clearReceiveBuffer();

    // XP协议方法
    // 发送XP协议握手命令
    bool sendXPHandshakeCommand(uint32_t timeout = 1000);

    // 发送XP协议出货命令
    bool sendXPDispenseCommand(uint8_t row, uint8_t column, uint32_t timeout = 1000);

    // 发送XP协议退币命令
    bool sendXPRefundCommand(uint8_t refundAmount1, uint8_t refundAmount2, uint32_t timeout = 1000);

    // 发送XP协议复位命令
    bool sendXPResetCommand(uint32_t timeout = 1000);

    // 处理XP协议响应
    void processXPResponse(uint8_t* data, uint8_t length);

    // 计算XP协议加密字节
    uint8_t generateXPEncryptByte();

    // 应用XP协议加密
    void applyXPEncryption(uint8_t* data, uint8_t length, uint8_t encryptByte);

    // 计算XP协议校验和
    uint8_t calculateXPChecksum(uint8_t* data, uint8_t length);

    // 验证XP协议校验和
    bool verifyXPChecksum(uint8_t* data, uint8_t length);
};

#endif // SERIAL_MOTOR_CONTROLLER_H
