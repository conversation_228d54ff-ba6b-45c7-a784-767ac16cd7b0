#ifndef COMMAND_PROCESSOR_H
#define COMMAND_PROCESSOR_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include "config.h"

// 前向声明
class MQTTClient;
class DeviceControl;
class Air780E;
class StatusManager;
class HardwareNetworkManager;
class WebServerManager;

// 出货结果结构体
struct DispenseResult {
    bool pending;                // 是否有待发送的结果
    StaticJsonDocument<512> originalDoc; // 原始命令JSON
    unsigned long startTime;     // 开始出货时间
    String api_address;
};

// API响应结构体
struct MemberOrderGoodsOutRes {
    String code;                 // 响应代码
    String data;                 // 响应数据
    String msg;                  // 响应消息
};

// 命令类型枚举
enum CommandType {
    CMD_UNKNOWN = 0,
    CMD_DISPENSE,
    CMD_MOTOR_CONTROL,
    CMD_GET_STATUS,
    CMD_GET_PARAM,
    CMD_SAVE_PARAM,
    CMD_GET_LOCATION,
    CMD_GPS_CONTROL,
    CMD_RESTART,
    CMD_RELAY_CONTROL,
    CMD_IR_CONTROL,
    CMD_VOICE_CONTROL,
    CMD_AUDIO_DOWNLOAD_LOCAL,
    CMD_AUDIO_DELETE_LOCAL
};

class CommandProcessor {
public:
    CommandProcessor();

    // 初始化命令处理器
    bool begin(MQTTClient* mqttClient, DeviceControl* deviceControl, Air780E* air780e, StatusManager* statusManager, HardwareNetworkManager* hardwareNetworkManager, WebServerManager* webServerManager);

    // 处理MQTT消息
    void processMqttMessage(String topic, String message);

    // MQTT回调函数
    static void mqttCallback(String topic, String message);

    // 处理事件（检查待发送的出货结果）
    void handleEvents();

private:
    MQTTClient* _mqttClient;
    DeviceControl* _deviceControl;
    Air780E* _air780e;
    StatusManager* _statusManager;
    HardwareNetworkManager* _hardwareNetworkManager;
    WebServerManager* _webServerManager;

    // 解析命令类型
    CommandType parseCommandType(const String& command);

    // 处理各种命令
    void processDispenseCommand(JsonDocument& doc);
    void processGetStatusCommand(JsonDocument& doc);
    void processGetParamCommand(JsonDocument& doc);
    void processSaveParamCommand(JsonDocument& doc);
    void processGetLocationCommand(JsonDocument& doc);
    void processGpsControlCommand(JsonDocument& doc);
    void processRestartCommand(JsonDocument& doc);
    void processRelayControlCommand(JsonDocument& doc);
    void processIRControlCommand(JsonDocument& doc);
    void processVoiceControlCommand(JsonDocument& doc);
    void processAudioDownloadCommand(JsonDocument& doc);
    void processAudioDownloadLocalCommand(JsonDocument& doc);
    void processAudioDeleteLocalCommand(JsonDocument& doc);

    // 发布命令结果
    void publishResult(JsonDocument& resultDoc, const JsonDocument& originalDoc, int qos = 0);

    // Base64解码函数
    uint8_t* base64Decode(const char* input, size_t inputLength, size_t* outputLength);

    // 检查设备状态并处理错误
    bool checkDeviceStatusAndHandleError(JsonDocument& doc, const char* commandResult);

    // 通过API接口发送出货结果
    bool sendDispenseResultViaAPI(const JsonDocument& originalDoc);

    // 通过API接口发布参数
    bool publishParamsViaAPI(JsonDocument& paramsDoc, const char* apiEndpoint);

    // 统一的API发送函数
    bool sendHttpRequest(const char* apiEndpoint, const String& payload, const String& contentType = "application/json", int timeout = 10000);

    // 解析JSON响应字符串为MemberOrderGoodsOutRes结构体
    MemberOrderGoodsOutRes parseJsonString_res(const String& jsonString);

    // 音频下载相关函数
    bool downloadAndSaveAudio(const String& url, uint8_t audioIndex);
    bool downloadAudioViaWiFi(const String& url, String& audioData);
    bool downloadAudioVia4G(const String& url, String& audioData);
    bool saveAudioToManager(uint8_t audioIndex, const uint8_t* data, size_t size);

    // 保存待发送的出货结果
    void savePendingDispenseResult(const JsonDocument &originalDoc);

    // 检查并发送待发送的出货结果
    void checkAndSendPendingDispenseResult();

    // 待发送的出货结果
    DispenseResult _pendingDispenseResult;
};

#endif // COMMAND_PROCESSOR_H
