#include "HardwareNetworkManager.h"

HardwareNetworkManager::HardwareNetworkManager(Air780E* air780e)
   : _air780e(air780e),
      _networkMode(NETWORK_MODE_WIFI),
      _isConnected(false),
      _apActive(false),
      _wifiConnecting(false),
      _wifiConnectStartTime(0),
      _wifiConnectTimeout(15000),
      _deviceId("") { // 初始化设备唯一ID为空字符串

    // 初始化配置，将从配置文件读取，仅初始化为空字符串，防止出现随机数据
    _wifiSSID[0] = '\0';
    _wifiPassword[0] = '\0';
    _apn[0] = '\0';
    _apnUser[0] = '\0';
    _apnPassword[0] = '\0';
}

bool HardwareNetworkManager::begin() {
    // 初始化网络管理(硬件初始化已经在各自模块中完成)
    // 获取设备唯一ID
    _deviceId = getUniqueDeviceId();

    if (_deviceId.length() > 0) {
        Serial.printf("设备唯一ID: %s\n", _deviceId.c_str());
        return true;
    } else {
        Serial.println("错误: 无法获取设备唯一ID，即将重启设备...");
        delay(1000);  // 等待1秒，让日志输出完成
        ESP.restart();
        return false;  // 这行代码实际上不会执行，因为设备会重启
    }
}

bool HardwareNetworkManager::configureWiFi(const char* ssid, const char* password) {
    strncpy(_wifiSSID, ssid, sizeof(_wifiSSID) - 1);
    strncpy(_wifiPassword, password, sizeof(_wifiPassword) - 1);
    return true;
}

bool HardwareNetworkManager::configure4G(const char* apn, const char* user, const char* password) {
    strncpy(_apn, apn, sizeof(_apn) - 1);
    strncpy(_apnUser, user, sizeof(_apnUser) - 1);
    strncpy(_apnPassword, password, sizeof(_apnPassword) - 1);
    return true;
}

bool HardwareNetworkManager::setNetworkMode(NetworkMode mode) {
    if (_networkMode != mode) {
        // 如果当前已连接，先断开
        // if (_isConnected) {
        //     disconnect();
        // }

        _networkMode = mode;

        // 确保AP模式始终处于活跃状态，无论网络模式如何
        // 这样可以确保在网络断开重连时，AP模式下的Web服务器仍然可用
        if (!_apActive) {
            String defaultSSID = getDefaultAPSSID();
            startAP(defaultSSID.c_str(), DEFAULT_AP_PASSWORD);
        }
    }
    return true;
}

bool HardwareNetworkManager::connect() {
    bool result = false;

    // 确保AP模式始终处于活跃状态，无论网络模式如何
    // 这样可以确保在网络断开重连时，AP模式下的Web服务器仍然可用
    if (!_apActive)
    {
        Serial.println("确保AP模式已启动（作为本地配置接口）");
        String defaultSSID = getDefaultAPSSID();
        startAP(defaultSSID.c_str(), DEFAULT_AP_PASSWORD);
    }

    // 根据当前网络模式连接
    if (_networkMode == NETWORK_MODE_WIFI)
    {
        Serial.println("使用wifi网络连接");
        result = connectWiFi();
    }
    else
    {
        // 4G模式
        Serial.println("使用4G网络连接");
        result = connect4G();
    }

    // 连接后再次检查状态
    _isConnected = isConnected();

    return _isConnected;
}

bool HardwareNetworkManager::disconnect() {
    if (_networkMode == NETWORK_MODE_WIFI) {
        WiFi.disconnect();
        _isConnected = false;
        return true;
    } else {
        bool result = _air780e->disconnect();
        _isConnected = false;
        return result;
    }
}

bool HardwareNetworkManager::isConnected() {
    if (_networkMode == NETWORK_MODE_WIFI) {
        _isConnected = WiFi.status() == WL_CONNECTED;
    } else {
        _isConnected = _air780e->isConnected();
    }
    return _isConnected;
}

NetworkMode HardwareNetworkManager::getNetworkMode() {
    return _networkMode;
}

bool HardwareNetworkManager::startAP(const char* ssid, const char* password) {
    // 如果AP已经活跃，先停止
    if (_apActive) {
        stopAP();
    }

    // 检查SSID和密码是否为空
    if (strlen(ssid) == 0 || strlen(password) == 0) {
        Serial.println("警告: AP模式的SSID或密码为空，使用默认值");
        String defaultSSID = getDefaultAPSSID();
        ssid = defaultSSID.c_str();
        password = DEFAULT_AP_PASSWORD;
    }

    Serial.printf("启动AP模式，SSID: %s，密码: %s\n", ssid, password);

    // 配置AP模式
    WiFi.disconnect(true);
    delay(100);
    WiFi.mode(WIFI_AP);
    delay(100);

    // 启动AP
    bool result = WiFi.softAP(ssid, password);
    if (result)
    {
        IPAddress apIP = WiFi.softAPIP();
        Serial.printf("AP启动成功，IP地址: %s\n", apIP.toString().c_str());

        // 停止之前的DNS服务器（如果有）
        _dnsServer.stop();

        // 启动DNS服务器，将所有域名解析到AP的IP地址
        // _dnsServer.start(53, "*", apIP);
        // Serial.println("DNS服务器已启动，将所有域名解析到AP的IP地址");

        _apActive = true;
    }
    else
    {
        Serial.println("AP启动失败");
    }

    return result;
}

bool HardwareNetworkManager::stopAP() {
    if (_apActive) {
        _dnsServer.stop();
        WiFi.softAPdisconnect(true);
        _apActive = false;
        return true;
    }
    return false;
}

bool HardwareNetworkManager::isAPActive()
{
    // 检查AP模式是否真正活跃
    bool reallyActive = (WiFi.getMode() == WIFI_AP || WiFi.getMode() == WIFI_AP_STA);
    if (_apActive != reallyActive)
    {
        Serial.printf("AP状态不一致: _apActive=%d, 实际=%d\n", _apActive, reallyActive);
        _apActive = reallyActive;
    }
    return _apActive;
}

String HardwareNetworkManager::getIPAddress() {
    if (_networkMode == NETWORK_MODE_WIFI) {
        if (WiFi.status() == WL_CONNECTED) {
            return WiFi.localIP().toString();
        }
    } else {
        if (_air780e->isConnected()) {
            return _air780e->getIPAddress();
        }
    }
    return "";
}

uint8_t HardwareNetworkManager::getSignalStrength() {
    if (_networkMode == NETWORK_MODE_WIFI) {
        // 即使WiFi未连接，也尝试获取信号强度
        // 这样可以在Web界面上显示当前可用的WiFi信号强度
        long rssi = WiFi.RSSI();
        if (rssi == 0 || rssi < -100) {
            // 如果RSSI为0或小于-100，表示无信号
            return 0;
        } else if (rssi >= -50) {
            return 100;
        } else {
            return 2 * (rssi + 100);
        }
    } else {
        // 4G模式下获取信号强度
        uint8_t strength = _air780e->getSignalStrength();
        return strength;
    }
}

String HardwareNetworkManager::getUniqueDeviceId() {
    // 如果已经获取过设备ID，直接返回
    if (_deviceId.length() > 0) {
        return _deviceId;
    }

    // 使用ESP32的芯片ID作为唯一标识符
    uint64_t chipId = ESP.getEfuseMac();
    char deviceId[17]; // 16个字符 + 结束符

    // 将64位整数格式化为16位十六进制字符串（不带冒号）
    sprintf(deviceId, "%016llX", chipId);

    // 保存设备ID
    _deviceId = String(deviceId);

    return _deviceId;
}

String HardwareNetworkManager::getDefaultAPSSID() {
    // 获取设备唯一ID
    String deviceId = getUniqueDeviceId();

    // 取设备ID的后12个字符
    String suffix = "";
    if (deviceId.length() >= 12) {
        suffix = deviceId.substring(deviceId.length() - 12);
    } else {
        suffix = deviceId; // 如果不足12个字符，使用全部
    }

    // 组合前缀和后缀
    String apSSID = String(DEFAULT_AP_SSID_PREFIX) + suffix;

    return apSSID;
}

void HardwareNetworkManager::handleEvents() {
    // 处理DNS请求(仅在AP模式下)
    if (_apActive) {
        _dnsServer.processNextRequest();
    }

    // 处理WiFi连接状态 - 只更新状态，不主动重连
    if (_wifiConnecting)
    {
        // 检查是否已连接
        if (WiFi.status() == WL_CONNECTED)
        {
            _wifiConnecting = false;
            _isConnected = true;
            Serial.printf("WiFi连接成功，IP: %s\n", WiFi.localIP().toString().c_str());
        }
        // 检查是否超时
        else if (millis() - _wifiConnectStartTime > _wifiConnectTimeout)
        {
            _wifiConnecting = false;
            _isConnected = false;
            Serial.printf("WiFi连接失败，状态码: %d\n", WiFi.status());
        }
        // 仍在连接中，打印进度
        else if (millis() % 1000 < 50)
        { // 大约每秒打印一次
            Serial.print(".");
        }
    }
}

bool HardwareNetworkManager::connectWiFi()
{
    // 检查当前WiFi模式，如果不是AP+STA混合模式，则设置为混合模式
    // 但不断开现有连接，避免影响AP模式
    if (WiFi.getMode() != WIFI_AP_STA) {
        Serial.println("设置WiFi为AP+STA混合模式");
        WiFi.mode(WIFI_AP_STA);
        delay(100);
    }

    // 确保AP模式处于活跃状态
    if (!_apActive)
    {
        // 启动AP模式，使用默认值
        // 注意：这里使用默认值是因为我们没有直接访问WebServerManager的配置
        // 在实际应用中，应该通过SystemCore获取WebServerManager的配置
        String defaultSSID = getDefaultAPSSID();
        startAP(defaultSSID.c_str(), DEFAULT_AP_PASSWORD);
    }

    // 断开当前WiFi连接，但不影响AP模式
    WiFi.disconnect(false);
    delay(100);

    // 连接WiFi
    Serial.printf("开始连接WiFi: %s\n", _wifiSSID);
    WiFi.begin(_wifiSSID, _wifiPassword);

    // 设置连接开始时间，但不阻塞等待
    _wifiConnectStartTime = millis();
    _wifiConnecting = true;

    // 立即返回，连接状态将在handleEvents中检查
    return true;
}

bool HardwareNetworkManager::connect4G() {
    // 检查是否有配置APN
    if (strlen(_apn) == 0) {
        return false;
    }

    // 连接4G网络
    bool result = _air780e->connect(_apn, _apnUser, _apnPassword);
    _isConnected = result;

    // 如果没有AP模式，启动AP模式
    if (!_apActive) {
        String defaultSSID = getDefaultAPSSID();
        startAP(defaultSSID.c_str(), DEFAULT_AP_PASSWORD);
    }

    return result;
}

// pauseWiFiConnection 和 resumeWiFiConnection 方法已移除
