#include "BluetoothManager.h"
#include "config.h"
#include "DeviceControl.h"
#include "StatusManager.h"
#include <ArduinoJson.h>

// 蓝牙回调类实现
BluetoothCallbacks::BluetoothCallbacks(BluetoothManager* manager)
    : _manager(manager)
{
}

void BluetoothCallbacks::onWrite(BLECharacteristic *pCharacteristic)
{
    // 获取UUID
    BLEUUID bleUuid = pCharacteristic->getUUID();
    String uuidStr = bleUuid.toString().c_str();

    // 获取值
    uint8_t* data = pCharacteristic->getData();
    size_t length = pCharacteristic->getLength();
    String valueStr = "";

    // 将数据转换为字符串
    if (length > 0) {
        char* charData = (char*)malloc(length + 1);
        if (charData) {
            memcpy(charData, data, length);
            charData[length] = 0; // 添加字符串结束符
            valueStr = String(charData);
            free(charData);
        }
    }

    Serial.printf("蓝牙特征值写入: UUID=%s, 值=%s\n", uuidStr.c_str(), valueStr.c_str());

    // 处理不同特征值的写入
    if (uuidStr == BLE_CONFIG_CHAR_UUID) {
        // 配置特征值
        _manager->handleConfigCommand(valueStr);
    } else if (uuidStr == BLE_COMMAND_CHAR_UUID) {
        // 命令特征值
        _manager->handleControlCommand(valueStr);
    }
}

void BluetoothCallbacks::onRead(BLECharacteristic *pCharacteristic)
{
    // 获取UUID
    BLEUUID bleUuid = pCharacteristic->getUUID();
    String uuidStr = bleUuid.toString().c_str();

    Serial.printf("蓝牙特征值读取: UUID=%s\n", uuidStr.c_str());

    if (uuidStr == BLE_STATUS_CHAR_UUID) {
        // 状态特征值，更新最新状态
        _manager->updateStatusCharacteristic();
    } else if (uuidStr == BLE_CONFIG_CHAR_UUID) {
        // 配置特征值，返回当前配置
        SystemConfig* config = _manager->getConfig();
        if (config != nullptr) {
            String configJson = _manager->createConfigJson();
            pCharacteristic->setValue(configJson.c_str());
            Serial.printf("返回配置: %s\n", configJson.c_str());
        }
    }
}

// 蓝牙服务器回调类实现
BluetoothServerCallbacks::BluetoothServerCallbacks(BluetoothManager* manager)
    : _manager(manager)
{
}

void BluetoothServerCallbacks::onConnect(BLEServer* pServer)
{
    Serial.println("蓝牙客户端已连接");
    _manager->setConnected(true);
}

void BluetoothServerCallbacks::onDisconnect(BLEServer* pServer)
{
    Serial.println("蓝牙客户端已断开连接");
    _manager->setConnected(false);

    // 重新开始广播
    pServer->startAdvertising();
    Serial.println("重新开始蓝牙广播");
}

// 蓝牙管理类实现
BluetoothManager::BluetoothManager()
    : _server(nullptr),
      _service(nullptr),
      _configCharacteristic(nullptr),
      _statusCharacteristic(nullptr),
      _commandCharacteristic(nullptr),
      _callbacks(nullptr),
      _serverCallbacks(nullptr),
      _connected(false),
      _initialized(false),
      _lastStatusUpdate(0),
      _config(nullptr),
      _status(nullptr),
      _deviceControl(nullptr),
      _statusManager(nullptr)
{
}

bool BluetoothManager::begin(SystemConfig* config, DeviceStatus* status, DeviceControl* deviceControl, StatusManager* statusManager)
{
    _config = config;
    _status = status;
    _deviceControl = deviceControl;
    _statusManager = statusManager;

    // 初始化蓝牙
    BLEDevice::init(BLE_DEVICE_NAME);

    // 创建蓝牙服务器
    _server = BLEDevice::createServer();
    if (_server == nullptr) {
        Serial.println("创建蓝牙服务器失败");
        return false;
    }

    // 设置服务器回调
    _serverCallbacks = new BluetoothServerCallbacks(this);
    _server->setCallbacks(_serverCallbacks);

    // 创建蓝牙服务
    _service = _server->createService(BLE_SERVICE_UUID);
    if (_service == nullptr) {
        Serial.println("创建蓝牙服务失败");
        return false;
    }

    // 创建特征值
    _configCharacteristic = _service->createCharacteristic(
        BLE_CONFIG_CHAR_UUID,
        BLECharacteristic::PROPERTY_READ |
        BLECharacteristic::PROPERTY_WRITE
    );

    _statusCharacteristic = _service->createCharacteristic(
        BLE_STATUS_CHAR_UUID,
        BLECharacteristic::PROPERTY_READ |
        BLECharacteristic::PROPERTY_NOTIFY
    );

    _commandCharacteristic = _service->createCharacteristic(
        BLE_COMMAND_CHAR_UUID,
        BLECharacteristic::PROPERTY_WRITE
    );

    // 添加描述符
    _statusCharacteristic->addDescriptor(new BLE2902());

    // 设置回调
    _callbacks = new BluetoothCallbacks(this);
    _configCharacteristic->setCallbacks(_callbacks);
    _statusCharacteristic->setCallbacks(_callbacks);
    _commandCharacteristic->setCallbacks(_callbacks);

    // 启动服务
    _service->start();

    // 开始广播
    BLEAdvertising* advertising = _server->getAdvertising();
    advertising->addServiceUUID(BLE_SERVICE_UUID);
    advertising->setScanResponse(true);
    advertising->setMinPreferred(0x06);  // 设置为iPhone可连接
    advertising->setMinPreferred(0x12);
    advertising->start();

    Serial.println("蓝牙初始化成功，开始广播");
    _initialized = true;

    return true;
}

void BluetoothManager::handleEvents()
{
    if (!_initialized) {
        return;
    }

    // 定期更新状态特征值
    unsigned long currentMillis = millis();
    if (_connected && currentMillis - _lastStatusUpdate > 1000) {
        _lastStatusUpdate = currentMillis;
        updateStatusCharacteristic();
    }
}

void BluetoothManager::updateStatusCharacteristic()
{
    if (!_initialized || _statusCharacteristic == nullptr || _status == nullptr) {
        return;
    }

    // 创建状态JSON
    String statusJson = createStatusJson();

    // 更新特征值
    _statusCharacteristic->setValue(statusJson.c_str());

    // 如果已连接，发送通知
    if (_connected) {
        _statusCharacteristic->notify();
    }
}

String BluetoothManager::createStatusJson()
{
    // 使用更小的JSON文档
    StaticJsonDocument<256> doc;

    // 只添加最重要的状态信息
    doc["wifi"] = _status->wifi_connected;
    doc["cell"] = _status->cellular_connected;
    doc["mqtt"] = _status->mqtt_connected;
    doc["motor"] = _status->motor_running;
    doc["ir1"] = _status->ir_triggered;
    doc["ir2"] = _status->ir_triggered2;
    doc["ir3"] = _status->ir_triggered3;
    doc["pir"] = _status->pir_triggered;

    // 简化继电器状态
    char relayStatus[6] = {0};
    for (int i = 0; i < 5; i++) {
        relayStatus[i] = _status->relay_status[i] ? '1' : '0';
    }
    doc["relay"] = relayStatus;

    doc["uptime"] = _status->uptime;
    doc["signal"] = _status->signal_strength;
    doc["err"] = _status->device_communication_error;
    doc["mac"] = _status->mac_address;

    // 序列化JSON
    String result;
    serializeJson(doc, result);
    return result;
}

String BluetoothManager::createConfigJson()
{
    // 使用更小的JSON文档
    StaticJsonDocument<256> doc;

    // 只添加最重要的配置信息
    doc["ssid"] = _config->wifi_ssid;
    doc["wifi_on"] = _config->wifi_enabled;
    doc["apn"] = _config->apn;
    doc["cell_on"] = _config->cellular_enabled;
    doc["net_mode"] = _config->network_mode;
    doc["mqtt_srv"] = _config->mqtt_server;
    doc["mqtt_port"] = _config->mqtt_port;
    doc["mqtt_user"] = _config->mqtt_user;
    doc["mqtt_id"] = _config->mqtt_client_id;
    doc["dev_id"] = _config->device_id;
    doc["dev_name"] = _config->device_name;

    // 序列化JSON
    String result;
    serializeJson(doc, result);
    return result;
}

bool BluetoothManager::parseConfigJson(const String& json)
{
    // 使用更小的JSON文档
    StaticJsonDocument<256> doc;
    DeserializationError error = deserializeJson(doc, json);

    if (error) {
        Serial.print("解析配置JSON失败: ");
        Serial.println(error.c_str());
        return false;
    }

    // 使用简化的键名
    // WiFi配置
    if (doc.containsKey("ssid")) {
        strlcpy(_config->wifi_ssid, doc["ssid"], sizeof(_config->wifi_ssid));
    }

    if (doc.containsKey("pass") && strlen(doc["pass"]) > 0) {
        strlcpy(_config->wifi_password, doc["pass"], sizeof(_config->wifi_password));
    }

    if (doc.containsKey("wifi_on")) {
        _config->wifi_enabled = doc["wifi_on"];
    }

    // 4G配置
    if (doc.containsKey("apn")) {
        strlcpy(_config->apn, doc["apn"], sizeof(_config->apn));
    }

    if (doc.containsKey("apn_user")) {
        strlcpy(_config->apn_user, doc["apn_user"], sizeof(_config->apn_user));
    }

    if (doc.containsKey("apn_pass") && strlen(doc["apn_pass"]) > 0) {
        strlcpy(_config->apn_password, doc["apn_pass"], sizeof(_config->apn_password));
    }

    if (doc.containsKey("cell_on")) {
        _config->cellular_enabled = doc["cell_on"];
    }

    // 网络模式
    if (doc.containsKey("net_mode")) {
        _config->network_mode = (NetworkMode)doc["net_mode"].as<int>();
    }

    // MQTT配置
    if (doc.containsKey("mqtt_srv")) {
        strlcpy(_config->mqtt_server, doc["mqtt_srv"], sizeof(_config->mqtt_server));
    }

    if (doc.containsKey("mqtt_port")) {
        _config->mqtt_port = doc["mqtt_port"];
    }

    if (doc.containsKey("mqtt_user")) {
        strlcpy(_config->mqtt_user, doc["mqtt_user"], sizeof(_config->mqtt_user));
    }

    if (doc.containsKey("mqtt_pass") && strlen(doc["mqtt_pass"]) > 0) {
        strlcpy(_config->mqtt_password, doc["mqtt_pass"], sizeof(_config->mqtt_password));
    }

    if (doc.containsKey("mqtt_id")) {
        strlcpy(_config->mqtt_client_id, doc["mqtt_id"], sizeof(_config->mqtt_client_id));
    }

    // 设备信息
    if (doc.containsKey("dev_id")) {
        strlcpy(_config->device_id, doc["dev_id"], sizeof(_config->device_id));
    }

    if (doc.containsKey("dev_name")) {
        strlcpy(_config->device_name, doc["dev_name"], sizeof(_config->device_name));
    }

    return true;
}

void BluetoothManager::handleConfigCommand(const String& command)
{
    // 解析配置命令
    if (parseConfigJson(command)) {
        Serial.println("配置更新成功");

        // 保存配置
        // 这里需要调用系统的保存配置方法
        // 假设StatusManager有一个saveConfig方法
        if (_statusManager != nullptr) {
            // _statusManager->saveConfig();
            Serial.println("配置已保存");
        }

        // 更新配置特征值
        if (_configCharacteristic != nullptr) {
            String configJson = createConfigJson();
            _configCharacteristic->setValue(configJson.c_str());
            Serial.printf("更新配置特征值: %s\n", configJson.c_str());
        }
    } else {
        Serial.println("配置更新失败");
    }
}

void BluetoothManager::handleControlCommand(const String& command)
{
    // 使用更小的JSON文档
    StaticJsonDocument<128> doc;
    DeserializationError error = deserializeJson(doc, command);

    if (error) {
        Serial.print("解析命令失败: ");
        Serial.println(error.c_str());
        return;
    }

    // 检查命令类型
    if (!doc.containsKey("cmd")) {
        Serial.println("缺少cmd字段");
        return;
    }

    String cmd = doc["cmd"];
    Serial.printf("命令: %s\n", cmd.c_str());

    if (cmd == "disp") {
        // 出货命令
        uint32_t ch = doc.containsKey("ch") ? doc["ch"] : DEFAULT_CHANNEL_NUMBER;
        _deviceControl->dispenseProduct(ch);
    } else if (cmd == "relay") {
        // 继电器控制命令
        if (doc.containsKey("n") && doc.containsKey("s")) {
            uint8_t n = doc["n"];
            bool s = doc["s"];
            _deviceControl->setRelay(n, s);
        } else if (doc.containsKey("all")) {
            bool s = doc["all"];
            _deviceControl->setAllRelays(s);
        }
    } else if (cmd == "voice") {
        // 语音控制命令
        // if (doc.containsKey("act")) {
        //     String act = doc["act"];
        //     if (act == "welcome") {
        //         _deviceControl->playWelcomeVoice();
        //     } else if (act == "play" && doc.containsKey("idx")) {
        //         uint8_t idx = doc["idx"];
        //         _deviceControl->playAudio(idx);
        //     } else if (act == "stop") {
        //         _deviceControl->stopAudio();
        //     } else if (act == "vol" && doc.containsKey("val")) {
        //         uint8_t vol = doc["val"];
        //         _deviceControl->setAudioVolume(vol);
        //     }
        // }
    } else if (cmd == "restart") {
        // 重启命令
        Serial.println("重启命令，3秒后重启");
        delay(3000);
        ESP.restart();
    }
}

bool BluetoothManager::isConnected() const
{
    return _connected;
}

void BluetoothManager::setConnected(bool connected)
{
    _connected = connected;
}

SystemConfig* BluetoothManager::getConfig() const
{
    return _config;
}

DeviceStatus* BluetoothManager::getStatus() const
{
    return _status;
}

DeviceControl* BluetoothManager::getDeviceControl() const
{
    return _deviceControl;
}

StatusManager* BluetoothManager::getStatusManager() const
{
    return _statusManager;
}
