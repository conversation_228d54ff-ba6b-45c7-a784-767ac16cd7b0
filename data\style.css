/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
header {
    background-color: #2c3e50;
    color: #fff;
    padding: 20px;
    border-radius: 5px 5px 0 0;
}

header h1 {
    margin-bottom: 10px;
}

.status-bar {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    font-size: 14px;
}

/* 导航样式 */
nav {
    background-color: #34495e;
    border-radius: 0 0 5px 5px;
}

nav ul {
    display: flex;
    list-style: none;
    overflow-x: auto;
}

nav ul li {
    flex: 1;
    min-width: 100px;
    text-align: center;
}

nav ul li a {
    display: block;
    color: #fff;
    text-decoration: none;
    padding: 15px;
    transition: background-color 0.3s;
}

nav ul li a:hover,
nav ul li a.active {
    background-color: #1abc9c;
}

/* 主内容区样式 */
main {
    background-color: #fff;
    padding: 20px;
    margin: 20px 0;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* 页面样式 */
.page {
    display: none;
}

.page.active {
    display: block;
}

h2 {
    margin-bottom: 20px;
    color: #2c3e50;
    border-bottom: 2px solid #ecf0f1;
    padding-bottom: 10px;
}

h3 {
    margin-bottom: 15px;
    color: #34495e;
}

/* 卡片样式 */
.card {
    background-color: #f9f9f9;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.status-card {
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.location-card {
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

/* 表单样式 */
.form-group {
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

input[type="text"],
input[type="password"],
input[type="number"],
select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

input[type="checkbox"] {
    margin-right: 5px;
}

/* 按钮样式 */
.action-buttons {
    margin-top: 20px;
    display: flex;
    gap: 10px;
}

button {
    background-color: #3498db;
    color: #fff;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #2980b9;
}

button[type="submit"] {
    background-color: #2ecc71;
}

button[type="submit"]:hover {
    background-color: #27ae60;
}

#restart-system {
    background-color: #e74c3c;
}

#restart-system:hover {
    background-color: #c0392b;
}

/* 结果框样式 */
.result-box {
    margin-top: 15px;
    padding: 10px;
    background-color: #ecf0f1;
    border-radius: 4px;
}

/* 传感器状态样式 */
.sensor-status p {
    margin: 8px 0;
    padding: 8px;
    background-color: #ecf0f1;
    border-radius: 4px;
}

/* 继电器控制样式 */
.relay-controls {
    margin-top: 10px;
}

.relay-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    padding: 8px;
    background-color: #ecf0f1;
    border-radius: 4px;
}

.relay-row span {
    margin-right: 10px;
}

.relay-row span:first-child {
    width: 80px;
}

.relay-row button {
    margin-left: 5px;
    padding: 5px 10px;
    font-size: 0.9em;
}

.relay-all-controls {
    margin-top: 15px;
    display: flex;
    justify-content: space-between;
}

.status-on {
    color: #27ae60;
    font-weight: bold;
}

.status-off {
    color: #7f8c8d;
}

.status-error {
    color: #e74c3c;
    font-weight: bold;
}

.status-ok {
    color: #27ae60;
}

/* 进度条样式 */
.progress-container {
    margin: 15px 0;
}

.progress-bar {
    height: 20px;
    background-color: #ecf0f1;
    border-radius: 10px;
    margin-bottom: 8px;
    overflow: hidden;
}

.progress {
    height: 100%;
    background-color: #3498db;
    width: 0%;
    transition: width 0.3s;
}

#upload-status {
    font-size: 14px;
    margin-bottom: 5px;
}

#upload-percentage {
    font-weight: bold;
    text-align: right;
    font-size: 14px;
}

.firmware-info {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #ecf0f1;
    border-radius: 4px;
}

.firmware-notes {
    margin-top: 20px;
    padding: 10px;
    background-color: #fef9e7;
    border-left: 4px solid #f39c12;
    border-radius: 4px;
    font-size: 14px;
}

.firmware-notes ul {
    margin-left: 20px;
    margin-top: 5px;
}

.form-text {
    display: block;
    margin-top: 5px;
    font-size: 12px;
    color: #7f8c8d;
}

#cancel-upload-btn {
    background-color: #e74c3c;
}

#cancel-upload-btn:hover {
    background-color: #c0392b;
}

/* 提示框样式 */
.toast {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: #fff;
    padding: 10px 20px;
    border-radius: 4px;
    opacity: 0;
    transition: opacity 0.3s;
    z-index: 1000;
}

.toast.show {
    opacity: 1;
}

/* 页脚样式 */
footer {
    text-align: center;
    padding: 20px;
    color: #7f8c8d;
    font-size: 14px;
}

/* 响应式样式 */
@media (max-width: 768px) {
    .status-grid {
        grid-template-columns: 1fr;
    }

    nav ul {
        flex-wrap: wrap;
    }

    nav ul li {
        flex: 1 1 50%;
    }
}
