#include "CommandProcessor.h"
#include "MQTTClient.h"
#include "DeviceControl.h"
#include "Air780E.h"
#include "StatusManager.h"
#include "SystemCore.h"
#include <HTTPClient.h>
#include <WiFiClient.h>
#include <WiFiClientSecure.h>
#include "HardwareNetworkManager.h"

// 声明外部SystemCore对象
extern SystemCore systemCore;

CommandProcessor::CommandProcessor()
    : _mqttClient(nullptr),
      _deviceControl(nullptr),
      _air780e(nullptr),
      _statusManager(nullptr),
      _hardwareNetworkManager(nullptr),
      _webServerManager(nullptr)
{
    // 初始化待发送的出货结果
    _pendingDispenseResult.pending = false;
    _pendingDispenseResult.startTime = 0;
    _pendingDispenseResult.api_address = "";
}

bool CommandProcessor::begin(MQTTClient *mqttClient, DeviceControl *deviceControl, Air780E *air780e, StatusManager *statusManager, HardwareNetworkManager *hardwareNetworkManager, WebServerManager *webServerManager)
{
    _mqttClient = mqttClient;
    _deviceControl = deviceControl;
    _air780e = air780e;
    _statusManager = statusManager;
    _hardwareNetworkManager = hardwareNetworkManager;
    _webServerManager = webServerManager;

    return true;
}

void CommandProcessor::mqttCallback(String topic, String message)
{
    // 将MQTT消息转发给系统核心的命令处理器
    systemCore.getCommandProcessor().processMqttMessage(topic, message);
}

void CommandProcessor::processMqttMessage(String topic, String message)
{
    Serial.println("收到MQTT消息: " + topic + " => " + message);

    StaticJsonDocument<512> doc;
    DeserializationError error = deserializeJson(doc, message);

    if (error)
    {
        Serial.print("解析JSON失败: ");
        Serial.println(error.c_str());

        // 尝试修复常见的JSON格式问题
        String fixedMessage = message;

        // 1. 移除末尾多余的逗号
        fixedMessage.replace(",}", "}");
        fixedMessage.replace(", }", "}");
        fixedMessage.replace(",\n}", "\n}");
        fixedMessage.replace(", \n}", "\n}");

        // 2. 确保属性名有引号
        // 这里可以添加更多的修复逻辑，但需要更复杂的字符串处理

        // 尝试用修复后的消息重新解析
        error = deserializeJson(doc, fixedMessage);
        if (error)
        {
            Serial.print("修复后仍然解析失败: ");
            Serial.println(error.c_str());
            return;
        }

        Serial.println("JSON格式已修复并成功解析");
    }

    // 处理命令
    if (doc.containsKey("topicCode"))
    {
        String commandStr = doc["topicCode"];
        CommandType command = parseCommandType(commandStr);

        switch (command)
        {
        case CMD_DISPENSE:
            _pendingDispenseResult.api_address = _webServerManager->getConfig().api_goodsOut;
            processDispenseCommand(doc);
            break;

        case CMD_MOTOR_CONTROL:
            _pendingDispenseResult.api_address = _webServerManager->getConfig().api_cabinetBinTest;
            processDispenseCommand(doc);
            break;

            // case CMD_GET_STATUS:
            //     processGetStatusCommand(doc);
            //     break;

        case CMD_GET_PARAM:
            processGetParamCommand(doc);
            break;

        case CMD_SAVE_PARAM:
            processSaveParamCommand(doc);
            break;

        case CMD_GET_LOCATION:
            processGetLocationCommand(doc);
            break;

        // case CMD_GPS_CONTROL:
        //     processGpsControlCommand(doc);
        //     break;

        case CMD_RESTART:
            processRestartCommand(doc);
            break;

        case CMD_RELAY_CONTROL:
            processRelayControlCommand(doc);
            break;

        case CMD_IR_CONTROL:
            processIRControlCommand(doc);
            break;

        // case CMD_VOICE_CONTROL:
        //     processVoiceControlCommand(doc);
        //     break;

        case CMD_AUDIO_DOWNLOAD_LOCAL:
            processAudioDownloadLocalCommand(doc);
            break;

        case CMD_AUDIO_DELETE_LOCAL:
            processAudioDeleteLocalCommand(doc);
            break;

        default:
            Serial.println("未知命令: " + commandStr);
            break;
        }
    }
}

CommandType CommandProcessor::parseCommandType(const String &command)
{
    if (command == "outGoods")
        return CMD_DISPENSE;
    if (command == "cabinetBinTest")
        return CMD_MOTOR_CONTROL;
    // if (command == "get_status")
    //     return CMD_GET_STATUS;
    if (command == "getParams")
        return CMD_GET_PARAM;
    if (command == "saveParams")
        return CMD_SAVE_PARAM;
    if (command == "getGpsInfo")
        return CMD_GET_LOCATION;
    if (command == "gps_control")
        return CMD_GPS_CONTROL;
    if (command == "restart")
        return CMD_RESTART;
    if (command == "switch_light")
        return CMD_RELAY_CONTROL;
    if (command == "switch_infraredDetection")
        return CMD_IR_CONTROL;
    // if (command == "voice_control")
    //     return CMD_VOICE_CONTROL;
    if (command == "audio_download_local")
        return CMD_AUDIO_DOWNLOAD_LOCAL;
    if (command == "audio_delete_local")
        return CMD_AUDIO_DELETE_LOCAL;

    return CMD_UNKNOWN;
}

// 检查设备状态并处理错误
bool CommandProcessor::checkDeviceStatusAndHandleError(JsonDocument &doc, const char* commandResult)
{
#if 0
    // 检查设备通信状态
    bool deviceCommunicationError = _deviceControl->isDeviceCommunicationError();

    // 检查设备状态（通过allowScan判断）
    bool deviceStatus = _deviceControl->getDeviceStatus();

    // 如果设备通信异常或设备状态异常，直接返回错误
    if (deviceCommunicationError || !deviceStatus)
    {
        // 创建结果JSON
        StaticJsonDocument<512> resultDoc;
        // 添加错误信息
        if (deviceCommunicationError)
        {
            resultDoc["outMsg"] = "设备通信异常，无法出货";
            Serial.println("设备通信异常，无法出货");
        }
        else if (!deviceStatus)
        {
            resultDoc["outMsg"] = "设备状态异常，不允许扫码支付";
            Serial.println("设备状态异常，不允许扫码支付");
        }
        resultDoc["outCode"] = 0;  // 电机是否出货的结果
        resultDoc["status"] = 0;
        // 通过API接口发送出货结果
        bool apiSent = sendDispenseResultViaAPI(resultDoc, doc);
        Serial.printf("通过API发送出货结果%s\n", apiSent ? "成功" : "失败");

        // 如果API发送失败，则通过MQTT发布结果作为备份
        if (!apiSent)
        {
            Serial.println("API发送失败，通过MQTT发布结果作为备份");
            publishResult(resultDoc, doc, 2);
        }

        return false;
    }
#endif
    return true;
}

void CommandProcessor::processDispenseCommand(JsonDocument &doc)
{
    // 解析JSON消息，标准格式
    // {
    // "topicCode": "outGoods", // 通道出货命令
    // "id":4086,
    // "spMemberOrderId":225400,  //当前出货订单号
    // "aisleRowNum":1,
    // "aisleColumnNum":2,
    // "anotherAisleRowNum":2,
    // "anotherAisleColumnNum":3,
    // "isDoubleAisle":1,
    // "createTime":1746600597279 // 与当前时间对比，超过120秒就丢弃
    // }
    // 先比对时间是否超过120秒
    if (doc.containsKey("createTime"))
    {
        // 获取命令中的时间字符串，格式为"YYYY-MM-DD HH:MM:SS"
        String datetimeStr = doc["createTime"].as<String>();
        Serial.printf("命令时间字符串: %s\n", datetimeStr.c_str());

        // 获取当前时间字符串
        String currentTimeStr = systemCore.getCurrentTimeString("%Y-%m-%d %H:%M:%S");
        Serial.printf("当前时间字符串: %s\n", currentTimeStr.c_str());

        // 直接比较字符串，简单判断是否合理
        if (datetimeStr.length() < 10 || currentTimeStr.length() < 10) {
            Serial.println("时间格式错误，继续执行");
        }

        // 解析命令时间字符串为tm结构
        struct tm cmdTime = {0};
        int year, month, day, hour, minute, second;
        if (sscanf(datetimeStr.c_str(), "%d-%d-%d %d:%d:%d",
                  &year, &month, &day, &hour, &minute, &second) == 6) {
            cmdTime.tm_year = year - 1900;
            cmdTime.tm_mon = month - 1;
            cmdTime.tm_mday = day;
            cmdTime.tm_hour = hour;
            cmdTime.tm_min = minute;
            cmdTime.tm_sec = second;

            // 获取当前时间
            time_t currentTime = systemCore.getCurrentUnixTime();
            time_t cmdTimestamp = mktime(&cmdTime);

            // 计算时间差（秒）
            int64_t timeDiff = (int64_t)currentTime - (int64_t)cmdTimestamp;

            Serial.printf("命令时间戳: %ld, 当前时间戳: %ld, 时间差: %lld 秒\n",
                         (long)cmdTimestamp, (long)currentTime, timeDiff);

            // 如果命令时间比当前时间晚（未来时间），允许执行
            if (timeDiff < 0) {
                Serial.println("警告: 命令时间比当前时间晚（未来时间），继续执行");
            }
            // 如果命令时间比当前时间早超过120秒，则丢弃
            else if (timeDiff > 120) {
                Serial.printf("出货命令已超时，时间差: %lld 秒，丢弃\n", timeDiff);
                return;
            }

            Serial.println("命令时间检查通过");
        } else {
            Serial.println("无法解析命令时间格式，继续执行");
        }
    }
    else
    {
        Serial.println("出货命令缺少时间戳，丢弃");
        return;
    }
    uint32_t aisleRowNum = doc.containsKey("aisleRowNum") ? doc["aisleRowNum"].as<uint32_t>() : 1;
    uint32_t aisleColumnNum = doc.containsKey("aisleColumnNum") ? doc["aisleColumnNum"].as<uint32_t>() : 1;
    uint32_t anotherAisleRowNum = doc.containsKey("anotherAisleRowNum") ? doc["anotherAisleRowNum"].as<uint32_t>() : 1;
    uint32_t anotherAisleColumnNum = doc.containsKey("anotherAisleColumnNum") ? doc["anotherAisleColumnNum"].as<uint32_t>() : 1;
    aisleColumnNum += 1;
    anotherAisleColumnNum += 1;

    // 打印解析出的参数值，便于调试
    Serial.printf("解析参数: aisleRowNum=%d, aisleColumnNum=%d, anotherAisleRowNum=%d, anotherAisleColumnNum=%d\n",
                 aisleRowNum, aisleColumnNum, anotherAisleRowNum, anotherAisleColumnNum);

    uint32_t channelNumber = aisleRowNum;
    uint32_t ColumnNumber = aisleColumnNum;
    // 根据接口类型选择不同的出货命令
    if (_deviceControl->getInterfaceType() == INTERFACE_SZ1_RS485)
    {
        channelNumber = aisleRowNum * 2 + aisleColumnNum;
        ColumnNumber = 0;
    }
    uint16_t quantity = doc.containsKey("quantity") ? doc["quantity"] : 1;
    // 检查是否是多通道出货
    bool isMultiChannel = doc.containsKey("isDoubleAisle") ? doc["isDoubleAisle"] == 1 : false;

    // 是否需要检测红外判断物品掉落
    bool isNeedDropDetection = doc.containsKey("saleDetetionState") ? doc["saleDetetionState"] == 1 : false;

    // 设置是否需要检测红外判断物品掉落
    _deviceControl->setNeedDropDetection(isNeedDropDetection);
    Serial.printf("出货命令设置红外检测状态: %s\n", isNeedDropDetection ? "启用" : "禁用");

    // 检查设备状态并处理错误
    // if (!checkDeviceStatusAndHandleError(doc, "dispense_result")) {
    //     return;
    // }

    // 保存待发送的出货结果
    savePendingDispenseResult(doc);

    // 执行出货
    if (isMultiChannel)
    {
        // 单通道多数量出货或多通道出货
        _deviceControl->dispenseMultiProduct(quantity, channelNumber, ColumnNumber, anotherAisleRowNum, anotherAisleColumnNum);
    }
    else
    {
        // 单通道出货
        _deviceControl->dispenseProduct(channelNumber, ColumnNumber);
    }
}

void CommandProcessor::processGetStatusCommand(JsonDocument &doc)
{
    // 发布状态
    Serial.println("收到获取状态命令，准备发布设备状态");

    // 先更新设备状态
    _statusManager->updateDeviceStatus();

    // 发布状态
    bool result = _statusManager->publishStatus(doc["topicCode"]);
    Serial.printf("发布状态%s\n", result ? "成功" : "失败");
}

void CommandProcessor::processGetParamCommand(JsonDocument &doc)
{
    // 获取并发布参数
    Serial.println("收到获取参数命令，准备发布参数");

    // 增加通过api接口发布参数的功能
    // publishParamsViaAPI(doc, _webServerManager->getConfig().api_getDeviceParams);

    // MQTT发布参数
    bool result = _statusManager->publishParams(doc["topicCode"]);
    Serial.printf("发布参数%s\n", result ? "成功" : "失败");
}

void CommandProcessor::processSaveParamCommand(JsonDocument &doc)
{
    // 保存参数
    Serial.println("收到保存参数命令，准备保存参数");
    // 只保存api服务器地址char api_goodsOut[200];char api_cabinetBinTest[200];
    if (doc.containsKey("api_goodsOut_url"))
    {
        strncpy(_webServerManager->getConfig().api_goodsOut, doc["api_goodsOut_url"], sizeof(_webServerManager->getConfig().api_goodsOut) - 1);
    }
    if (doc.containsKey("api_cabinetBinTest_url"))
    {
        strncpy(_webServerManager->getConfig().api_cabinetBinTest, doc["api_cabinetBinTest_url"], sizeof(_webServerManager->getConfig().api_cabinetBinTest) - 1);
    }
    // 保存配置
    bool result = _webServerManager->saveConfig();

    // 通过mqtt发布成功或失败结果到
    StaticJsonDocument<128> resultDoc;
    resultDoc["topicCode"] = doc["topicCode"];
    resultDoc["status"] = result? 1 : 0;

    // 发布结果
    publishResult(resultDoc, doc);
}

void CommandProcessor::processGetLocationCommand(JsonDocument &doc)
{
    // 获取并发布位置
    Serial.println("收到获取位置命令，准备发布位置");

    // 更新位置
    bool locationUpdated = _statusManager->updateLocation();

    // 不管获取位置成功还是失败都需要发布
    bool result = _statusManager->publishLocation(doc["topicCode"]);
    Serial.printf("发布位置%s\n", result ? "成功" : "失败");
}

void CommandProcessor::processGpsControlCommand(JsonDocument &doc)
{
    // GPS控制命令
    bool enable = doc.containsKey("enable") ? doc["enable"].as<bool>() : true;
    bool result = false;

    if (enable)
    {
        // 开启GPS
        Serial.println("收到开启GPS命令");
        result = _air780e->gpsOpen();
    }
    else
    {
        // 关闭GPS
        Serial.println("收到关闭GPS命令");
        result = _air780e->gpsClose();
    }

    // 发布结果
    StaticJsonDocument<256> resultDoc;
    resultDoc["command"] = "gps_control_result";
    resultDoc["success"] = result;
    resultDoc["gps_enabled"] = _air780e->isGpsOpen();

    // 发布结果
    publishResult(resultDoc, doc);
}

void CommandProcessor::processRestartCommand(JsonDocument &doc)
{
    // 重启系统
    Serial.println("收到重启命令，系统将在3秒后重启");

    // 发布结果
    StaticJsonDocument<128> resultDoc;
    resultDoc["command"] = "restart_result";
    resultDoc["message"] = "系统将在3秒后重启";

    // 发布结果
    publishResult(resultDoc, doc);

    // 延迟3秒后重启
    delay(3000);
    systemCore.restart();
}

void CommandProcessor::processRelayControlCommand(JsonDocument &doc)
{
    // 继电器控制命令
    bool success = false;
    String message = "";
    uint8_t relayNumber = 0;

    // 检查是否包含继电器编号
    if (doc.containsKey("number"))
    {
        relayNumber = doc["number"];

        // 检查是否包含状态
        if (doc.containsKey("status"))
        {
            bool state = doc["status"];

            // 设置继电器状态
            success = _deviceControl->setRelay(relayNumber, state);
        }
        else if (doc.containsKey("toggle"))
        {
            // 切换继电器状态
            bool currentState = _deviceControl->getRelayStatus(relayNumber);
            success = _deviceControl->setRelay(relayNumber, !currentState);
        }
        else
        {
            message = "缺少状态参数 (state 或 toggle)";
            Serial.println(message);
        }
    }
    else if (doc.containsKey("all"))
    {
        // 设置所有继电器状态
        bool state = doc["all"];
        success = _deviceControl->setAllRelays(state);
    }
    else
    {
        // 检查是否包含状态
        if (doc.containsKey("status"))
        {
            bool state = doc["status"];

            // 设置继电器状态(使用默认继电器1)
            relayNumber = 1;
            success = _deviceControl->setRelay(relayNumber, state);
        }
        else
        {
            message = "缺少继电器编号参数 (relay_number 或 all)";
            Serial.println(message);
        }
    }

    // 创建结果JSON
    StaticJsonDocument<256> resultDoc;
    resultDoc["topicCode"] = doc["topicCode"];
    resultDoc["number"] = relayNumber;

    // 添加继电器状态
    bool relayStatus[5];
    _deviceControl->getAllRelayStatus(relayStatus);
    if (relayNumber <= 5)
    {
        resultDoc["status"] = relayStatus[(relayNumber - 1)] ? 1 : 0;
    }
    else
    {
        resultDoc["status"] = message;
    }

    // 发布结果
    publishResult(resultDoc, doc);
}

void CommandProcessor::processIRControlCommand(JsonDocument &doc)
{
    if (doc.containsKey("number"))
    {
        uint8_t relayNumber = doc["number"];

        // 检查是否包含状态
        if (doc.containsKey("status"))
        {
            bool state = doc["status"] > 0 ? true : false;

            // 通过识别状态为1，触发红外功能：播放欢迎语音
            if (relayNumber == 1 && state)
            {
                // 播放欢迎语音
                _deviceControl->playWelcomeVoice();
            }
        }
    }

    // 获取红外传感器状态
    Serial.println("收到获取红外传感器状态命令");

    // 创建结果JSON
    StaticJsonDocument<256> resultDoc;
    resultDoc["topicCode"] = doc["topicCode"];
    resultDoc["number"] = doc["number"];
    resultDoc["status"] = doc["status"];

    // 发布结果
    publishResult(resultDoc, doc);
}

void CommandProcessor::processVoiceControlCommand(JsonDocument &doc)
{
    // 语音控制命令
    bool success = false;
    String message = "";

    // 检查是否包含操作类型
    if (doc.containsKey("action")) {
        String action = doc["action"].as<String>();

        // if (action == "welcome") {
        //     // 播放欢迎语音
        //     success = _deviceControl->playWelcomeVoice();
        //     message = "播放欢迎语音";
        // } else if (action == "play" && doc.containsKey("index")) {
        //     // 播放指定索引的语音
        //     uint8_t index = doc["index"];
        //     success = _deviceControl->playAudio(index);
        //     message = String("播放语音: 索引=") + index;
        // } else if (action == "stop") {
        //     // 停止播放
        //     success = _deviceControl->stopAudio();
        //     message = "停止播放语音";
        // } else if (action == "volume" && doc.containsKey("level")) {
        //     // 设置音量
        //     uint8_t volume = doc["level"];
        //     success = _deviceControl->setAudioVolume(volume);
        //     message = String("设置音量: ") + volume;
        // } else if (action == "upload" && doc.containsKey("index") && doc.containsKey("data")) {
        //     // 上传音频数据
        //     uint8_t index = doc["index"];
        //     String base64Data = doc["data"].as<String>();

        //     // 解码Base64数据
        //     size_t decodedLength = 0;
        //     uint8_t* decodedData = base64Decode(base64Data.c_str(), base64Data.length(), &decodedLength);

        //     if (decodedData != nullptr) {
        //         // 保存音频数据
        //         success = _deviceControl->saveAudioData(index, decodedData, decodedLength);
        //         message = String("上传音频数据: 索引=") + index + ", 大小=" + decodedLength + "字节";

        //         // 释放解码后的数据
        //         free(decodedData);
        //     } else {
        //         message = "Base64解码失败";
        //     }
        // } else {
        //     message = "未知的语音控制操作: " + action;
        // }
    } else {
        message = "缺少操作类型参数 (action)";
    }

    Serial.println(message);

    // 创建结果JSON
    StaticJsonDocument<256> resultDoc;
    resultDoc["command"] = "voice_control_result";
    resultDoc["success"] = success;
    resultDoc["message"] = message;

    // 发布结果
    publishResult(resultDoc, doc);
}

// Base64解码函数
uint8_t* CommandProcessor::base64Decode(const char* input, size_t inputLength, size_t* outputLength) {
    // Base64解码表
    const char* base64Chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";

    // 计算解码后的长度
    size_t paddingCount = 0;
    if (inputLength > 0) {
        if (input[inputLength - 1] == '=') paddingCount++;
        if (inputLength > 1 && input[inputLength - 2] == '=') paddingCount++;
    }

    *outputLength = ((inputLength + 3) / 4) * 3 - paddingCount;

    // 分配内存
    uint8_t* output = (uint8_t*)malloc(*outputLength);
    if (output == nullptr) {
        Serial.println("内存分配失败");
        *outputLength = 0;
        return nullptr;
    }

    // 解码
    size_t i = 0, j = 0;
    uint32_t sextet[4];

    while (i < inputLength) {
        // 获取4个6位值
        for (size_t k = 0; k < 4; k++) {
            if (i < inputLength && input[i] != '=') {
                const char* p = strchr(base64Chars, input[i]);
                if (p) {
                    sextet[k] = p - base64Chars;
                } else {
                    sextet[k] = 0;
                }
            } else {
                sextet[k] = 0;
            }
            i++;
        }

        // 组合成3个8位值
        uint32_t triple = (sextet[0] << 18) + (sextet[1] << 12) + (sextet[2] << 6) + sextet[3];

        if (j < *outputLength) output[j++] = (triple >> 16) & 0xFF;
        if (j < *outputLength) output[j++] = (triple >> 8) & 0xFF;
        if (j < *outputLength) output[j++] = triple & 0xFF;
    }

    return output;
}

void CommandProcessor::publishResult(JsonDocument &resultDoc, const JsonDocument &originalDoc, int qos)
{
    // 添加消息ID，如果原始命令中包含message_id
    // if (originalDoc.containsKey("message_id"))
    // {
    //     resultDoc["message_id"] = originalDoc["message_id"];
    // }

    // 添加时间戳（使用Unix时间戳，毫秒）
    // resultDoc["timestamp"] = systemCore.getCurrentUnixTimeMs();

    // 序列化JSON
    String resultPayload;
    serializeJson(resultDoc, resultPayload);

    // 检查JSON格式
    StaticJsonDocument<512> checkDoc;
    DeserializationError error = deserializeJson(checkDoc, resultPayload);
    if (error)
    {
        Serial.print("结果JSON格式错误: ");
        Serial.println(error.c_str());
        return;
    }

    // 发布结果
    bool published = _mqttClient->publishStatusTopic(resultPayload);

    // 打印发布的消息内容
    Serial.print("发布的消息内容: ");
    Serial.println(resultPayload);
}

bool CommandProcessor::sendDispenseResultViaAPI(const JsonDocument &originalDoc)
{
    // 出货结果
    uint8_t dispenseStatus = _deviceControl->getDispenseStatus();      // 电机转动出货的结果状态
    DeliveryResultData lastDispenseResult = _deviceControl->getLastDispenseResult(); // 最终出货结果，通过mqtt下发决定是否包含红外检测到物品掉落
    String outMsg = "";

    // 创建结果JSON
    StaticJsonDocument<512> resultDoc;
    StaticJsonDocument<512> resultStatusDoc;

    // 添加错误信息
    if (lastDispenseResult.aisleState)
    {
        // 先判断是否要检测红外
        if (_deviceControl->isNeedDropDetection())
        {
            if (!lastDispenseResult.finalShipmentResult)
            { // 电机出货正常，但是红外没有检测到物品掉落
                outMsg = "电机出货正常，红外感应失败";
                Serial.println("电机出货正常，红外感应失败");
            }
            else
            {
                outMsg = "出货成功, 红外感应成功";
                Serial.println("出货成功, 红外感应成功");
            }
        }
        else
        {
            if (!lastDispenseResult.finalShipmentResult)
            {
                outMsg = "电机出货失败，原因未知";
                Serial.println("电机出货失败，原因未知");
            }
            else
            {
                outMsg = "出货成功, 无红外感应";
                Serial.println("出货成功, 无红外感应");
            }
        }
    }
    else
    {
        outMsg = "电机出货失败";
        Serial.println("电机出货失败");
    }
    resultDoc["aisleState"] = lastDispenseResult.aisleState; // 电机转动出货的结果
    resultDoc["anotherAisleRowNum"] = originalDoc["anotherAisleRowNum"];
    resultDoc["aisleRowNum"] = originalDoc["aisleRowNum"];
    resultDoc["saleDetectionResult"] = lastDispenseResult.saleDetectionResult;
    resultDoc["anotherAisleColumnNum"] = originalDoc["anotherAisleColumnNum"];
    resultDoc["runTime"] = systemCore.getCurrentTimeString("%Y-%m-%d %H:%M:%S");
    resultDoc["aisleColumnNum"] = originalDoc["aisleColumnNum"];

    // 创建完整的响应文档
    DynamicJsonDocument docAll(1024);
    docAll["id"] = originalDoc["id"];
    if (originalDoc.containsKey("spMemberOrderId"))
    {
        docAll["spMemberOrderId"] = originalDoc["spMemberOrderId"];
    }
    else if (originalDoc.containsKey("spCabinetBinTestId"))
    {
        docAll["spCabinetBinTestId"] = originalDoc["spCabinetBinTestId"];
    }

    resultStatusDoc["status"] = resultDoc["aisleState"];
    resultStatusDoc["deliveryResultData"] = resultDoc;
    resultStatusDoc["outCode"] = resultDoc["aisleState"] ? 0 : 1;
    resultStatusDoc["outMsg"] = outMsg;
    resultStatusDoc["outTime"] = resultDoc["runTime"];

    docAll["jsonOutStatus"] = resultStatusDoc;

    // 序列化JSON
    String resultPayload;
    serializeJson(docAll, resultPayload);
    Serial.println(resultPayload);

    // 检查JSON格式
    StaticJsonDocument<1024> checkDoc;
    DeserializationError error = deserializeJson(checkDoc, resultPayload);
    if (error)
    {
        Serial.print("结果JSON格式错误: ");
        Serial.println(error.c_str());
        return false;
    }

    // 获取API服务器地址，如果原始命令中包含api_server则使用它，否则使用默认地址
    const char* apiServer = originalDoc.containsKey("api_server") ? originalDoc["api_server"].as<const char *>() : _pendingDispenseResult.api_address.c_str();

    // 使用统一的API发送函数
    return sendHttpRequest(apiServer, resultPayload);
}

bool CommandProcessor::sendHttpRequest(const char *apiEndpoint, const String &payload, const String &contentType, int timeout)
{
    // 检查当前网络模式
    int networkMode = _hardwareNetworkManager->getNetworkMode();
    bool success = false;
    String response = "";

    // 检查URL是否以https开头
    String url = String(apiEndpoint);
    bool isHttps = url.startsWith("https://");

    Serial.printf("API URL协议: %s\n", isHttps ? "HTTPS" : "HTTP");

    if (_hardwareNetworkManager->isConnected())
    {
        if (networkMode == NETWORK_MODE_WIFI)
        {
            // 使用WiFi发送HTTP/HTTPS请求
            Serial.println("使用WiFi发送请求...");

            if (isHttps) {
                // 使用HTTPS
                Serial.println("使用HTTPS协议...");

                #ifdef ESP32
                // 创建HTTPS客户端
                WiFiClientSecure client;
                HTTPClient https;

                // 设置为不验证服务器证书（生产环境中应该验证证书）
                client.setInsecure();

                // 设置超时时间
                https.setTimeout(timeout);

                // 开始HTTPS请求
                https.begin(client, apiEndpoint);
                https.addHeader("Content-Type", contentType);

                // 发送POST请求
                Serial.println("通过WiFi API发送数据(HTTPS)...");
                Serial.print("API地址: ");
                Serial.println(apiEndpoint);
                Serial.print("发送数据: ");
                Serial.println(payload);

                int httpCode = https.POST(payload);

                // 检查HTTPS响应
                if (httpCode > 0)
                {
                    Serial.printf("HTTPS响应码: %d\n", httpCode);

                    if (httpCode == HTTP_CODE_OK)
                    {
                        response = https.getString();
                        Serial.println("服务器响应: " + response);
                        success = true;
                    }
                }
                else
                {
                    Serial.printf("HTTPS请求失败，错误: %s\n", https.errorToString(httpCode).c_str());
                }

                https.end();
                #else
                Serial.println("当前平台不支持HTTPS请求");
                #endif
            } else {
                // 使用HTTP
                Serial.println("使用HTTP协议...");

                // 创建HTTP客户端
                WiFiClient client;
                HTTPClient http;

                // 设置超时时间
                http.setTimeout(timeout);

                // 开始HTTP请求
                http.begin(client, apiEndpoint);
                http.addHeader("Content-Type", contentType);

                // 发送POST请求
                Serial.println("通过WiFi API发送数据(HTTP)...");
                Serial.print("API地址: ");
                Serial.println(apiEndpoint);
                Serial.print("发送数据: ");
                Serial.println(payload);

                int httpCode = http.POST(payload);

                // 检查HTTP响应
                if (httpCode > 0)
                {
                    Serial.printf("HTTP响应码: %d\n", httpCode);

                    if (httpCode == HTTP_CODE_OK)
                    {
                        response = http.getString();
                        Serial.println("服务器响应: " + response);
                        success = true;
                    }
                }
                else
                {
                    Serial.printf("HTTP请求失败，错误: %s\n", http.errorToString(httpCode).c_str());
                }

                http.end();
            }
        }
        else if (networkMode == NETWORK_MODE_4G)
        {
            // 使用4G模块发送HTTP/HTTPS请求
            Serial.println("使用4G模块发送请求...");

            // 发送POST请求
            Serial.printf("通过4G API发送数据(%s)...\n", isHttps ? "HTTPS" : "HTTP");
            Serial.print("API地址: ");
            Serial.println(apiEndpoint);
            Serial.print("发送数据: ");
            Serial.println(payload);

            // 使用4G模块发送HTTP/HTTPS POST请求
            // AIR780E模块应该能够自动处理HTTP和HTTPS
            response = _air780e->httpPost(apiEndpoint, contentType.c_str(), payload.c_str(), timeout);

            if (response.length() > 0)
            {
                Serial.println("4G请求成功");
                Serial.print("服务器响应: ");
                Serial.println(response);
                success = true;
            }
            else
            {
                Serial.println("4G请求失败");
            }
        }
        // 解析响应JSON
        if (response.length() > 0) {
            MemberOrderGoodsOutRes res = parseJsonString_res(response);
            if (res.code != "200")
            {
                Serial.println("res.msg:" + res.msg);
            }
            else
            {
                if (res.data != "null")
                {
                    Serial.println("res.data:" + res.data);
                    // 继续出货
                    StaticJsonDocument<512> doc;
                    DeserializationError error = deserializeJson(doc, res.data);
                    if (!error) {
                        processDispenseCommand(doc);
                    } else {
                        Serial.println("解析res.data失败: " + String(error.c_str()));
                    }
                }
                else
                {
                    Serial.println("res.data:" + res.data);
                }
            }
        }
    }
    else
    {
        Serial.println("无法发送HTTP请求: WiFi和4G都未连接");
    }

    return success;
}

bool CommandProcessor::publishParamsViaAPI(JsonDocument &paramsDoc, const char *apiEndpoint)
{
    // 添加设备标识信息
    // paramsDoc["device_id"] = _hardwareNetworkManager->getUniqueDeviceId();
    // paramsDoc["device_name"] = DEVICE_NAME;
    // paramsDoc["timestamp"] = systemCore.getCurrentUnixTimeMs();

    // 添加MQTT配置
    paramsDoc["mqtt_broker"] = _webServerManager->getConfig().mqtt_server;
    paramsDoc["mqtt_port"] = _webServerManager->getConfig().mqtt_port;
    paramsDoc["mqtt_username"] = _webServerManager->getConfig().mqtt_user;
    paramsDoc["mqtt_password"] = _webServerManager->getConfig().mqtt_password;

    // 添加WiFi配置
    paramsDoc["wifi_ssid"] = _webServerManager->getConfig().wifi_ssid;
    paramsDoc["wifi_password"] = _webServerManager->getConfig().wifi_password;
    paramsDoc["web_server_ssid"] = _webServerManager->getConfig().web_server_ssid;
    paramsDoc["web_server_password"] = _webServerManager->getConfig().web_server_password;

    // 添加API接口配置
    paramsDoc["goodsOut"] = _webServerManager->getConfig().api_goodsOut;
    paramsDoc["cabinetBinTest"] = _webServerManager->getConfig().api_cabinetBinTest;
    // paramsDoc["getDeviceParams"] = _webServerManager->getConfig().api_getDeviceParams;

    // 创建完整的响应文档
    DynamicJsonDocument docAll(512);
    docAll["code"] = "200";
    docAll["data"] = paramsDoc;
    docAll["msg"] = "success";

    // 序列化JSON
    String payload;
    serializeJson(docAll, payload);

    // 检查JSON格式
    StaticJsonDocument<512> checkDoc;
    DeserializationError error = deserializeJson(checkDoc, payload);
    if (error)
    {
        Serial.print("参数JSON格式错误: ");
        Serial.println(error.c_str());
        return false;
    }

    // 使用统一的API发送函数
    return sendHttpRequest(apiEndpoint, payload);
}

void CommandProcessor::savePendingDispenseResult(const JsonDocument &originalDoc)
{
    // 保存待发送的出货结果
    _pendingDispenseResult.pending = true;
    _pendingDispenseResult.startTime = millis();

    // 复制原始命令JSON
    _pendingDispenseResult.originalDoc.clear();
    _pendingDispenseResult.originalDoc.set(originalDoc);

    Serial.println("已保存待发送的出货结果，等待出货完成后发送");
}

void CommandProcessor::checkAndSendPendingDispenseResult()
{
    // 检查是否有待发送的出货结果
    if (!_pendingDispenseResult.pending)
    {
        return;
    }

    // 获取当前出货状态
    uint8_t dispenseStatus = _deviceControl->getDispenseStatus();

    // 检查设备是否正在出货中
    if (_deviceControl->isDispensing())
    {
        // 如果出货状态为进行中，继续等待
        if (dispenseStatus == DISPENSE_STATUS_IN_PROGRESS)
        {
            // 每10秒打印一次等待信息
            unsigned long currentTime = millis();
            static unsigned long lastPrintTime = 0;
            if (currentTime - lastPrintTime > 10000)
            {
                lastPrintTime = currentTime;
                Serial.println("出货状态为进行中，继续等待出货完成后发送结果");
            }
            return;
        }
    }

    // 清除待发送的出货结果
    _pendingDispenseResult.pending = false;

    bool apiSent = sendDispenseResultViaAPI(_pendingDispenseResult.originalDoc);
    Serial.printf("通过API发送出货结果%s\n", apiSent ? "成功" : "失败");

    // 如果API发送失败，则通过MQTT发布结果作为备份
    // if (!apiSent)
    // {
    //     Serial.println("API发送失败，通过MQTT发布结果作为备份");
    //     publishResult(resultDoc, _pendingDispenseResult.originalDoc, 2);
    // }
}

void CommandProcessor::handleEvents()
{
    // 检查并发送待发送的出货结果
    checkAndSendPendingDispenseResult();
}

MemberOrderGoodsOutRes CommandProcessor::parseJsonString_res(const String& jsonString)
{
    MemberOrderGoodsOutRes result;

    // 默认值
    result.code = "";
    result.data = "null";
    result.msg = "";

    // 如果JSON字符串为空，直接返回默认值
    if (jsonString.length() == 0) {
        return result;
    }

    // 解析JSON
    StaticJsonDocument<1024> doc;
    DeserializationError error = deserializeJson(doc, jsonString);

    if (error) {
        Serial.print("解析JSON失败: ");
        Serial.println(error.c_str());
        return result;
    }

    // 提取字段
    if (doc.containsKey("code")) {
        result.code = doc["code"].as<String>();
    }

    if (doc.containsKey("data")) {
        // 如果data是对象或数组，将其序列化为字符串
        if (doc["data"].is<JsonObject>() || doc["data"].is<JsonArray>()) {
            String dataStr;
            serializeJson(doc["data"], dataStr);
            result.data = dataStr;
        } else {
            // 否则直接获取字符串值
            result.data = doc["data"].as<String>();
        }
    }

    if (doc.containsKey("msg")) {
        result.msg = doc["msg"].as<String>();
    }

    return result;
}

bool CommandProcessor::downloadAndSaveAudio(const String& url, uint8_t audioIndex)
{
    // 检查网络连接
    if (!_hardwareNetworkManager->isConnected()) {
        Serial.println("音频URL保存失败: 网络未连接");
        return false;
    }

    // 验证URL格式
    if (!url.startsWith("http://") && !url.startsWith("https://")) {
        Serial.println("音频URL保存失败: URL格式无效");
        return false;
    }

    Serial.printf("保存音频URL: %s (索引: %d)\n", url.c_str(), audioIndex);

    // 直接保存URL到AudioManager（新的方式）
    return _deviceControl->saveAudioURL(audioIndex, url);
}

void CommandProcessor::processAudioDownloadLocalCommand(JsonDocument &doc)
{
    // 本地音频下载命令
    // 预期JSON格式:
    // {
    //   "topicCode": "audio_download_local",
    //   "audio_url": "http://example.com/audio.mp3",
    //   "audio_index": 0,
    //   "audio_name": "welcome",
    //   "set_as_pir_audio": true
    // }

    bool success = false;
    String message = "";
    uint8_t audioIndex = 0;
    String audioUrl = "";

    Serial.println("收到本地音频下载命令");

    // 检查必需参数
    if (!doc.containsKey("audio_url")) {
        message = "缺少音频URL参数";
        Serial.println(message);
    } else if (!doc.containsKey("audio_index")) {
        message = "缺少音频索引参数";
        Serial.println(message);
    } else {
        audioUrl = doc["audio_url"].as<String>();
        audioIndex = doc["audio_index"].as<uint8_t>();

        // 验证音频索引范围
        if (audioIndex >= AUDIO_COUNT)
        {
            message = "音频索引超出范围";
            Serial.println(message);
        }
        else
        {
            Serial.printf("开始下载音频到本地: URL=%s, 索引=%d\n", audioUrl.c_str(), audioIndex);

            // 下载音频文件到本地
            success = _deviceControl->downloadAudioFile(audioIndex, audioUrl);

            if (success) {
                message = "音频下载到本地成功";
                Serial.println(message);

                // 如果指定了设置为PIR音频，则设置人体红外感应器播放的音频索引
                if (doc.containsKey("set_as_pir_audio") && doc["set_as_pir_audio"].as<bool>()) {
                    _deviceControl->setPIRAudioIndex(audioIndex);
                    message += "，已设置为人体红外感应器音频";
                }
            } else {
                message = "音频下载到本地失败";
                Serial.println(message);
            }
        }
    }

    // 创建结果JSON
    StaticJsonDocument<256> resultDoc;
    resultDoc["topicCode"] = doc["topicCode"];
    resultDoc["command"] = "audio_download_local_result";
    resultDoc["success"] = success;
    resultDoc["message"] = message;
    resultDoc["audio_index"] = audioIndex;
    resultDoc["audio_url"] = audioUrl;

    // 发布结果
    publishResult(resultDoc, doc);
}

void CommandProcessor::processAudioDeleteLocalCommand(JsonDocument &doc)
{
    // 删除本地音频文件命令
    // 预期JSON格式:
    // {
    //   "topicCode": "audio_delete_local",
    //   "audio_index": 0
    // }

    bool success = false;
    String message = "";
    uint8_t audioIndex = 0;

    Serial.println("收到删除本地音频文件命令");

    // 检查必需参数
    if (!doc.containsKey("audio_index")) {
        message = "缺少音频索引参数";
        Serial.println(message);
    } else {
        audioIndex = doc["audio_index"].as<uint8_t>();

        // 验证音频索引范围
        if (audioIndex >= AUDIO_COUNT) {
            message = "音频索引超出范围";
            Serial.println(message);
        } else {
            Serial.printf("开始删除本地音频文件: 索引=%d\n", audioIndex);

            // 检查文件是否存在
            if (!_deviceControl->localAudioExists(audioIndex)) {
                message = "本地音频文件不存在";
                success = true; // 文件不存在也算成功
                Serial.println(message);
            } else {
                // 删除本地音频文件
                success = _deviceControl->deleteLocalAudio(audioIndex);

                if (success) {
                    message = "本地音频文件删除成功";
                    Serial.println(message);
                } else {
                    message = "本地音频文件删除失败";
                    Serial.println(message);
                }
            }
        }
    }

    // 创建结果JSON
    StaticJsonDocument<256> resultDoc;
    resultDoc["topicCode"] = doc["topicCode"];
    resultDoc["command"] = "audio_delete_local_result";
    resultDoc["success"] = success;
    resultDoc["message"] = message;
    resultDoc["audio_index"] = audioIndex;

    // 发布结果
    publishResult(resultDoc, doc);
}
