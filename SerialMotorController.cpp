#include "SerialMotorController.h"

SerialMotorController::SerialMotorController()
    : _serial(nullptr),
      _interfaceType(INTERFACE_SZ1_RS232),
      _lastDispenseStatus(DISPENSE_STATUS_FAILED),
      _lastQueryTime(0),
      _receiveIndex(0),
      _frameStarted(false),
      _currentChannelNumber(0),
      _currentColumnNumber(0),
      _currentQuantity(0),
      _isMultiChannel(false),
      _waitingForResponse(false),
      _lastCommandTime(0),
      _responseTimeout(1000), // 1秒超时
      _queryCount(0),
      _maxQueryCount(3),               // 最多查询3次，约9秒
      _allowScan(false),                // 默认不允许扫码支付
      _queryTimeoutCount(0),            // 查询超时计数初始化为0
      _deviceCommunicationError(false), // 设备通信异常初始化为false
      _lastQueryResponseTime(0)         // 最后一次查询响应时间初始化为0
{
    memset(_receiveBuffer, 0, sizeof(_receiveBuffer));
}

bool SerialMotorController::begin(SerialInterfaceType interfaceType)
{
    _interfaceType = interfaceType;

    // 根据接口类型选择串口
    if (_interfaceType == INTERFACE_SZ1_RS232) {
        _serial = &MOTOR_SERIAL; // 在config.h中定义的串口

        // 使用115200波特率
        int baudRate = (_interfaceType == INTERFACE_SZ1_RS232) ? 115200 : 9600;
        _serial->begin(baudRate, SERIAL_8N1, PIN_MOTOR_RX, PIN_MOTOR_TX);

        Serial.printf("初始化串口控制器，接口类型: %d, 波特率: %d\n", _interfaceType, baudRate);
    } else {
        _serial = &RS485_SERIAL; // 在config.h中定义的串口
        _serial->begin(9600, SERIAL_8N1, PIN_RS485_RX, PIN_RS485_TX);

        // 配置RS485方向控制引脚
        pinMode(PIN_RS485_DE, OUTPUT);
        digitalWrite(PIN_RS485_DE, LOW); // 默认为接收模式

        Serial.println("初始化RS485串口控制器，波特率: 9600");
    }

    clearReceiveBuffer();

    // 根据接口类型发送初始化命令
    if (_interfaceType == INTERFACE_SZ1_RS232) {
        // XP协议发送握手命令
        Serial.println("发送XP协议握手命令");
        sendXPHandshakeCommand(_responseTimeout);
        _allowScan = true;
    }
    else
    {
        // 原协议发送查询命令
        Serial.println("发送原协议查询命令");
        sendQueryCommand(true, _responseTimeout);
    }

    return true;
}

void SerialMotorController::setInterfaceType(SerialInterfaceType type)
{
    if (_interfaceType != type) {
        _interfaceType = type;
        begin(_interfaceType);
    }
}

bool SerialMotorController::queryDevice(bool isNetworkConnected)
{
    // 每10秒发送一次查询命令
    uint32_t currentTime = millis();
    if (currentTime - _lastQueryTime >= 10000 || _lastQueryTime == 0) {
        _lastQueryTime = currentTime;

        // 根据接口类型选择不同的查询方式
        if (_interfaceType == INTERFACE_SZ1_RS232) {
            // XP协议使用握手命令查询设备状态
            return sendXPHandshakeCommand(_responseTimeout);
        }
        else
        {
            // 原协议使用查询命令
            return sendQueryCommand(isNetworkConnected, _responseTimeout);
        }
    }
    return false;
}

bool SerialMotorController::dispenseSingleChannel(uint32_t channelNumber, uint32_t ColumnNumber)
{
    Serial.printf("出货: 行=%d, 列=%d\n", channelNumber, ColumnNumber);

    // 根据接口类型选择不同的出货命令
    if (_interfaceType == INTERFACE_SZ1_RS232) {     
        _responseTimeout = 6000;
        return sendXPDispenseCommand(channelNumber, ColumnNumber, _responseTimeout);
    } else {
        // 原协议使用通道号
        // 保存当前出货信息
        _currentChannelNumber = channelNumber;
        _currentColumnNumber = ColumnNumber;
        _isMultiChannel = false;
        // 重置查询次数
        _queryCount = 0;
        return sendSingleDispenseCommand(channelNumber, ColumnNumber, _responseTimeout);
    }
}

/*
函数说明
    多通道出货
参数说明
    channelNumber: 通道号
    ColumnNumber: 列号
    anotherChannelNumber: 另一个通道号
    anothercolumnNumber: 另一个列号
    quantity: 数量
返回值
    成功返回true，失败返回false
*/
bool SerialMotorController::dispenseMultiChannel(uint32_t channelNumber, uint32_t ColumnNumber, uint16_t anotherChannelNumber, uint32_t anothercolumnNumber, uint16_t quantity)
{
    Serial.printf("出货: 行=%d, 列=%d, 数量=%d\n", channelNumber, ColumnNumber, quantity);
    // 根据接口类型选择不同的出货命令,以下暂时都不支持双通道同时出货
    if (_interfaceType == INTERFACE_SZ1_RS232) {
        _responseTimeout = 6000;
        return sendXPDispenseCommand(channelNumber, ColumnNumber, _responseTimeout);
    } else {
        // 原协议使用通道号和数量
        // 保存当前出货信息
        _currentChannelNumber = channelNumber;
        _currentColumnNumber = ColumnNumber;
        _anotherChannelNumber = anotherChannelNumber;
        _anotherColumnNumber = anothercolumnNumber;
        _currentQuantity = quantity;
        _isMultiChannel = true;
        // 重置查询次数
        _queryCount = 0;
        return sendMultiDispenseCommand(channelNumber, ColumnNumber, quantity, _responseTimeout);
    }
}

void SerialMotorController::handleReceivedData()
{
    if (_interfaceType == INTERFACE_SZ1_RS232) {
        // XP协议处理
        while (_serial->available()) {
            uint8_t byte = _serial->read();
            // 把收到的每一个数据都打印
            // Serial.printf("收到数据: 0x%02X\n", byte);

            // 检测XP协议帧头1
            if (byte == XP_FRAME_HEADER1 && !_frameStarted) {
                _frameStarted = true;
                _receiveBuffer[0] = byte;
                _receiveIndex = 1;
                continue;
            }

            // 如果已经开始接收帧
            if (_frameStarted) {
                // 防止缓冲区溢出
                if (_receiveIndex < sizeof(_receiveBuffer)) {
                    _receiveBuffer[_receiveIndex++] = byte;
                }

                // 检测XP协议帧头2（第二个字节）
                if (_receiveIndex == 2 && byte != XP_FRAME_HEADER2) {
                    // 不是有效的XP协议帧头2，重置接收状态
                    _frameStarted = false;
                    clearReceiveBuffer();
                    continue;
                }

                // 检测XP协议帧尾
                if (byte == XP_FRAME_FOOTER && _receiveIndex >= 8) { // 完整帧至少8字节
                    // 验证XP协议校验和(实际不校验)
                    // if (verifyXPChecksum(_receiveBuffer, _receiveIndex)) {
                        // 处理XP协议响应
                        processXPResponse(_receiveBuffer, _receiveIndex);
                    // } else {
                    //     Serial.println("XP协议校验和错误");
                    // }
                    // 如果之前有通信异常，现在恢复正常
                    if (_deviceCommunicationError) {
                        Serial.println("设备通信恢复正常");
                        _deviceCommunicationError = false;
                    }
                    // 重置接收状态
                    _waitingForResponse = false;
                    _frameStarted = false;
                    clearReceiveBuffer();
                }
            }
        }
    } else {
        // 原协议处理
        while (_serial->available()) {
            uint8_t byte = _serial->read();
            // 把收到的每一个数据都打印
            // Serial.printf("收到数据: 0x%02X\n", byte);

            // 检测帧头
            if (byte == FRAME_HEADER && !_frameStarted) {
                _frameStarted = true;
                _receiveBuffer[0] = byte;
                _receiveIndex = 1;
                continue;
            }

            // 如果已经开始接收帧
            if (_frameStarted) {
                // 防止缓冲区溢出
                if (_receiveIndex < sizeof(_receiveBuffer)) {
                    _receiveBuffer[_receiveIndex++] = byte;
                }

                // 检测帧尾
                if (byte == FRAME_FOOTER) {
                    // 处理完整的帧
                    uint8_t command = _receiveBuffer[1];
                    uint8_t dataLength = _receiveBuffer[2];

                    // 验证帧长度
                    if (_receiveIndex >= (dataLength + 5)) { // 帧头(1) + 命令(1) + 长度(1) + 数据(N) + 校验(2) + 帧尾(1)
                        // 验证校验和
                        if (verifyChecksum(_receiveBuffer, _receiveIndex)) {
                            // 根据命令类型处理
                            switch (command) {
                                case CMD_QUERY_RESPONSE:
                                    processQueryResponse(_receiveBuffer, _receiveIndex);
                                    break;
                                case CMD_DISPENSE_SINGLE_RESPONSE:
                                    processSingleDispenseResponse(_receiveBuffer, _receiveIndex);
                                    break;
                                case CMD_DISPENSE_MULTI_RESPONSE:
                                    processMultiDispenseResponse(_receiveBuffer, _receiveIndex);
                                    break;
                                default:
                                    Serial.printf("未知命令: 0x%02X\n", command);
                                    // 打印所有接收到的串口数据
                                    for (int i = 0; i < _receiveIndex; i++) {
                                        Serial.printf("0x%02X ", _receiveBuffer[i]);
                                    }
                                    Serial.println();
                                    break;
                            }

                            // 如果之前有通信异常，现在恢复正常
                            if (_deviceCommunicationError) {
                                Serial.println("设备通信恢复正常");
                                _deviceCommunicationError = false;
                            }
                        } else {
                            Serial.println("校验和错误");
                        }
                    } else {
                        Serial.println("帧长度错误");
                    }

                    // 重置接收状态
                    _frameStarted = false;
                    clearReceiveBuffer();
                }
            }
        }
    }
}

uint8_t SerialMotorController::getLastDispenseStatus()
{
    return _lastDispenseStatus;
}

bool SerialMotorController::getAllowScanStatus()
{
    return _allowScan;
}

bool SerialMotorController::isDeviceCommunicationError()
{
    return _deviceCommunicationError;
}

// 获取通信接口类型
SerialInterfaceType SerialMotorController::getInterfaceType()
{
    return _interfaceType;
}

void SerialMotorController::resetDeviceCommunicationError()
{
    _deviceCommunicationError = false;
    _queryTimeoutCount = 0;
    Serial.println("设备通信异常状态已重置");
}

void SerialMotorController::handleEvents()
{
    if (_interfaceType == INTERFACE_SZ1_RS232)
    {
        // INTERFACE_SZ1_RS232协议电机不直接返回正在出货状态，直到电机成功或失败才返回数据
        return;
    }

    // 处理接收到的数据
    handleReceivedData();

    // 获取当前时间
    uint32_t currentTime = millis();

    // 如果出货状态为进行中，每3秒重发一次查询命令
    if (_lastDispenseStatus == DISPENSE_STATUS_IN_PROGRESS) {
        if (currentTime - _lastCommandTime >= 3000) {
            // 检查是否超过最大查询次数
            if (_queryCount >= _maxQueryCount) {
                Serial.printf("已达到最大查询次数(%d)，停止查询，设置出货状态为失败\n", _maxQueryCount);
                _lastDispenseStatus = DISPENSE_STATUS_FAILED;
                _queryCount = 0;
                return;
            }

            // 增加查询次数
            _queryCount++;

            Serial.printf("出货状态为进行中，重新查询出货状态... (第%d次查询，最多%d次)\n", _queryCount, _maxQueryCount);
            _lastCommandTime = currentTime; // 更新最后命令时间
           
            // 原协议使用通道号
            if (_isMultiChannel) {
                Serial.printf("重新查询多通道出货状态: 通道=%d, 数量=%d\n", _currentChannelNumber, _currentQuantity);
                sendMultiDispenseCommand(_currentChannelNumber, _currentColumnNumber, _currentQuantity, _responseTimeout);
            } else {
                Serial.printf("重新查询单通道出货状态: 通道=%d\n", _currentChannelNumber);
                sendSingleDispenseCommand(_currentChannelNumber, _currentColumnNumber, _responseTimeout);
            }
        }
    } else {
        // 如果状态不是进行中，重置查询次数
        if (_queryCount > 0) {
            Serial.printf("出货状态已变更为%d，重置查询次数\n", _lastDispenseStatus);
            _queryCount = 0;
        }

        // 每10秒查询一次设备状态
        if (currentTime - _lastQueryTime >= 10000) {
            _lastQueryTime = currentTime;

            // 查询设备状态，传入网络连接状态（这里假设网络已连接）
            bool isNetworkConnected = true; // 可以从其他地方获取实际的网络状态

            Serial.println("定期查询设备状态...");
            bool result = false;

            // 原协议使用查询命令
            result = sendQueryCommand(isNetworkConnected, _responseTimeout);

            if (result) {
                Serial.printf("设备状态查询命令发送成功\n");
            } else {
                Serial.println("设备状态查询命令发送失败");

                // 增加查询超时计数
                if (_queryTimeoutCount < 255)
                {
                    _queryTimeoutCount++;
                }
                else
                {
                    _queryTimeoutCount = 255;
                }
                Serial.printf("查询超时计数: %d\n", _queryTimeoutCount);

                // 如果连续3次查询超时，则标记设备通信异常
                if (_queryTimeoutCount >= 3 && !_deviceCommunicationError) {
                    _deviceCommunicationError = true;
                    Serial.println("警告: 设备通信异常! 连续3次查询超时");
                }
            }
        }
    }
}

bool SerialMotorController::sendQueryCommand(bool isNetworkConnected, uint32_t timeout)
{
    uint8_t command[6]; // 帧头(1) + 命令(1) + 长度(1) + 数据(1) + 校验(2) + 帧尾(1)

    command[0] = FRAME_HEADER;
    command[1] = CMD_QUERY_DEVICE;
    command[2] = 0x01; // 数据长度
    command[3] = isNetworkConnected ? 0x00 : 0x01; // 0x00:已联网, 0x01:未联网

    // 计算校验和
    uint8_t checksum[2];
    calculateChecksum(&command[1], 3, checksum);
    command[4] = checksum[0];
    command[5] = checksum[1];

    // 帧尾
    uint8_t footer = FRAME_FOOTER;

    // 清空接收缓冲区
    while (_serial->available()) {
        _serial->read();
    }

    // 如果是RS485，切换为发送模式
    if (_interfaceType == INTERFACE_SZ1_RS485) {
        digitalWrite(PIN_RS485_DE, HIGH);
        delay(1);
    }

    // 发送命令
    _serial->write(command, 6);
    _serial->write(footer);
    _serial->flush();

    // 如果是RS485，切换为接收模式
    if (_interfaceType == INTERFACE_SZ1_RS485) {
        digitalWrite(PIN_RS485_DE, LOW);
    }

    Serial.print("发送查询命令: ");
    for (int i = 0; i < 6; i++) {
        Serial.printf("0x%02X ", command[i]);
    }
    Serial.printf("0x%02X\n", footer);

    // 等待响应
    bool responseReceived = waitForResponse(timeout);

    return responseReceived;
}

void SerialMotorController::processQueryResponse(uint8_t* data, uint8_t length)
{
    // 查询响应格式: 帧头(1) + 命令(1) + 长度(1) + 数据(9) + 校验(2) + 帧尾(1)
    if (length < 15) {
        Serial.println("查询响应长度错误");
        return;
    }

    uint8_t dataLength = data[2];
    if (dataLength != 0x09) {
        Serial.println("查询响应数据长度错误");
        return;
    }

    // 解析数据
    uint8_t allowScan = data[5]; // 是否允许扫码支付

    // 更新允许扫码支付状态（0x00表示允许，0x01表示不允许）
    _allowScan = (allowScan == 0x00);

    Serial.printf("查询响应: 允许扫码支付=%d (设备状态=%s)\n",
                 _allowScan,
                 _allowScan ? "正常" : "异常");

    // 更新最后一次查询响应时间
    _lastQueryResponseTime = millis();

    // 重置查询超时计数
    if (_queryTimeoutCount > 0) {
        Serial.println("收到查询响应，重置查询超时计数");
        _queryTimeoutCount = 0;
    }

    _waitingForResponse = false;
}

bool SerialMotorController::sendSingleDispenseCommand(uint32_t channelNumber, uint32_t ColumnNumber, uint32_t timeout)
{
    uint8_t command[20]; // 帧头(1) + 命令(1) + 长度(1) + 数据(15) + 校验(2) + 帧尾(1)

    command[0] = FRAME_HEADER;
    command[1] = CMD_DISPENSE_SINGLE;
    command[2] = 0x0F; // 数据长度

    // 支付唯一码(10字节)
    for (int i = 0; i < 10; i++) {
        command[3 + i] = 0;
    }

    // 保留字节
    command[13] = 0x00;

    // 购买通道号(4字节，低字节在前)
    command[14] = channelNumber & 0xFF;
    command[15] = (channelNumber >> 8) & 0xFF;
    command[16] = (channelNumber >> 16) & 0xFF;
    command[17] = (channelNumber >> 24) & 0xFF;

    // 计算校验和
    uint8_t checksum[2];
    calculateChecksum(&command[1], 17, checksum);
    command[18] = checksum[0];
    command[19] = checksum[1];

    // 帧尾
    uint8_t footer = FRAME_FOOTER;

    // 清空接收缓冲区
    while (_serial->available()) {
        _serial->read();
    }

    // 如果是RS485，切换为发送模式
    if (_interfaceType == INTERFACE_SZ1_RS485) {
        digitalWrite(PIN_RS485_DE, HIGH);
        delay(1);
    }

    // 发送命令
    _serial->write(command, 20);
    _serial->write(footer);
    _serial->flush();

    // 如果是RS485，切换为接收模式
    if (_interfaceType == INTERFACE_SZ1_RS485) {
        digitalWrite(PIN_RS485_DE, LOW);
    }

    Serial.print("发送单通道出货命令: ");
    for (int i = 0; i < 20; i++) {
        Serial.printf("0x%02X ", command[i]);
    }
    Serial.printf("0x%02X\n", footer);

    // 等待响应
    bool responseReceived = waitForResponse(timeout);

    return responseReceived;
}

void SerialMotorController::processSingleDispenseResponse(uint8_t* data, uint8_t length)
{
    // 单通道出货响应格式: 帧头(1) + 命令(1) + 长度(1) + 数据(15) + 校验(2) + 帧尾(1)
    if (length < 21) {
        Serial.println("单通道出货响应长度错误");
        return;
    }

    uint8_t dataLength = data[2];
    if (dataLength != 0x0F) {
        Serial.println("单通道出货响应数据长度错误");
        return;
    }

    // 解析数据
    uint8_t dispenseStatus = data[13]; // 出货状态

    // 解析通道号
    uint32_t channelNumber = data[14] | (data[15] << 8) | (data[16] << 16) | (data[17] << 24);

    Serial.printf("单通道出货响应: 状态=%d, 通道=%d\n", dispenseStatus, channelNumber);

    _lastDispenseStatus = dispenseStatus;
    _waitingForResponse = false;

    // 如果状态不是进行中，重置查询次数
    if (dispenseStatus != DISPENSE_STATUS_IN_PROGRESS) {
        Serial.printf("出货状态不是进行中，重置查询次数\n");
        _queryCount = 0;
    }
}

bool SerialMotorController::sendMultiDispenseCommand(uint32_t channelNumber, uint32_t ColumnNumber, uint16_t quantity, uint32_t timeout)
{
    uint8_t command[20]; // 帧头(1) + 命令(1) + 长度(1) + 数据(15) + 校验(2) + 帧尾(1)

    command[0] = FRAME_HEADER;
    command[1] = CMD_DISPENSE_MULTI;
    command[2] = 0x0F; // 数据长度

    // 支付唯一码(10字节)
    for (int i = 0; i < 10; i++) {
        command[3 + i] = 0;
    }

    // 保留字节
    command[13] = 0x00;

    // 购买通道号(2字节，低字节在前)
    command[14] = channelNumber & 0xFF;
    command[15] = (channelNumber >> 8) & 0xFF;

    // 出货数量(2字节，低字节在前)
    command[16] = quantity & 0xFF;
    command[17] = (quantity >> 8) & 0xFF;

    // 计算校验和
    uint8_t checksum[2];
    calculateChecksum(&command[1], 17, checksum);
    command[18] = checksum[0];
    command[19] = checksum[1];

    // 帧尾
    uint8_t footer = FRAME_FOOTER;

    // 清空接收缓冲区
    while (_serial->available()) {
        _serial->read();
    }

    // 如果是RS485，切换为发送模式
    if (_interfaceType == INTERFACE_SZ1_RS485) {
        digitalWrite(PIN_RS485_DE, HIGH);
        delay(1);
    }

    // 发送命令
    _serial->write(command, 20);
    _serial->write(footer);
    _serial->flush();

    // 如果是RS485，切换为接收模式
    if (_interfaceType == INTERFACE_SZ1_RS485) {
        digitalWrite(PIN_RS485_DE, LOW);
    }

    Serial.print("发送多通道出货命令: ");
    for (int i = 0; i < 20; i++) {
        Serial.printf("0x%02X ", command[i]);
    }
    Serial.printf("0x%02X\n", footer);

    // 等待响应
    bool responseReceived = waitForResponse(timeout);

    return responseReceived;
}

void SerialMotorController::processMultiDispenseResponse(uint8_t* data, uint8_t length)
{
    // 多通道出货响应格式: 帧头(1) + 命令(1) + 长度(1) + 数据(15) + 校验(2) + 帧尾(1)
    if (length < 21) {
        Serial.println("多通道出货响应长度错误");
        return;
    }

    uint8_t dataLength = data[2];
    if (dataLength != 0x0F) {
        Serial.println("多通道出货响应数据长度错误");
        return;
    }

    // 解析数据
    uint8_t dispenseStatus = data[13]; // 出货状态

    // 解析通道号
    uint16_t channelNumber = data[14] | (data[15] << 8);

    // 解析数量
    uint16_t quantity = data[16] | (data[17] << 8);

    Serial.printf("多通道出货响应: 状态=%d, 通道=%d, 数量=%d\n", dispenseStatus, channelNumber, quantity);

    _lastDispenseStatus = dispenseStatus;
    _waitingForResponse = false;

    // 如果状态不是进行中，重置查询次数
    if (dispenseStatus != DISPENSE_STATUS_IN_PROGRESS) {
        Serial.printf("出货状态不是进行中，重置查询次数\n");
        _queryCount = 0;
    }
}

void SerialMotorController::calculateChecksum(uint8_t* data, uint8_t length, uint8_t* checksum)
{
    uint16_t sum = 0;

    // 累加和
    for (uint8_t i = 0; i < length; i++) {
        sum += data[i];
    }

    // 低字节在前，高字节在后
    checksum[0] = sum & 0xFF;
    checksum[1] = (sum >> 8) & 0xFF;
}

bool SerialMotorController::verifyChecksum(uint8_t* data, uint8_t length)
{
    // 帧格式: 帧头(1) + 命令(1) + 长度(1) + 数据(N) + 校验(2) + 帧尾(1)
    // 校验和 = 命令 + 长度 + 数据

    if (length < 6) { // 至少需要帧头、命令、长度、校验和帧尾
        return false;
    }

    uint8_t dataLength = data[2];
    uint8_t checksumPos = 3 + dataLength;

    if (checksumPos + 3 > length) { // 确保有足够的数据
        return false;
    }

    uint16_t receivedChecksum = data[checksumPos] | (data[checksumPos + 1] << 8);

    uint16_t calculatedSum = 0;
    for (uint8_t i = 1; i < checksumPos; i++) {
        calculatedSum += data[i];
    }

    return receivedChecksum == calculatedSum;
}

void SerialMotorController::clearReceiveBuffer()
{
    memset(_receiveBuffer, 0, sizeof(_receiveBuffer));
    _receiveIndex = 0;
    _frameStarted = false;
}

bool SerialMotorController::waitForResponse(uint32_t timeout)
{
    _waitingForResponse = true;
    _lastCommandTime = millis();

    uint32_t startTime = millis();
    while (millis() - startTime < timeout) {
        // 处理接收到的数据
        handleReceivedData();

        // 如果已经收到响应，返回成功
        if (!_waitingForResponse) {
            return true;
        }

        // 短暂延时，避免CPU占用过高
        delay(10);
    }

    // 超时，未收到响应
    _waitingForResponse = false;
    Serial.println("等待响应超时");
    return false;
}

// XP协议方法实现
bool SerialMotorController::sendXPHandshakeCommand(uint32_t timeout)
{
    uint8_t command[8]; // 帧头(2) + 命令(1) + 数据(2) + 加密(1) + 校验(1) + 帧尾(1)

    command[0] = XP_FRAME_HEADER1;  // 'X'
    command[1] = XP_FRAME_HEADER2;  // 'P'
    command[2] = XP_CMD_HANDSHAKE;  // 握手命令
    command[3] = 0xE0;              // 数据字节1
    command[4] = 0xE0;              // 数据字节2

    // 生成加密字节
    uint8_t encryptByte = generateXPEncryptByte();
    command[5] = encryptByte;

    // 计算校验和
    command[6] = calculateXPChecksum(command, 6);

    // 应用加密
    // applyXPEncryption(&command[3], 2, encryptByte); // 加密命令和数据字节

    // 帧尾
    command[7] = XP_FRAME_FOOTER;  // 'L'

    // 清空接收缓冲区
    while (_serial->available()) {
        _serial->read();
    }

    // 发送命令
    _serial->write(command, 8);
    _serial->flush();

    Serial.print("发送XP握手命令: ");
    for (int i = 0; i < 8; i++) {
        Serial.printf("0x%02X ", command[i]);
    }
    Serial.println();

    // 等待响应
    bool responseReceived = waitForResponse(timeout);

    return responseReceived;
}

bool SerialMotorController::sendXPDispenseCommand(uint8_t row, uint8_t column, uint32_t timeout)
{
    uint8_t command[8]; // 帧头(2) + 命令(1) + 数据(2) + 加密(1) + 校验(1) + 帧尾(1)

    command[0] = XP_FRAME_HEADER1;  // 'X'
    command[1] = XP_FRAME_HEADER2;  // 'P'
    command[2] = XP_CMD_DISPENSE;   // 出货命令
    command[3] = row;               // 行号
    command[4] = column;            // 列号

    // 生成加密字节
    uint8_t encryptByte = generateXPEncryptByte();
    command[5] = encryptByte;

    // 计算校验和
    command[6] = calculateXPChecksum(command, 6);

    // 应用加密
    // applyXPEncryption(&command[3], 2, encryptByte); // 加密命令和数据字节

    // 帧尾
    command[7] = XP_FRAME_FOOTER;  // 'L'

    // 清空接收缓冲区
    while (_serial->available()) {
        _serial->read();
    }

    // 发送命令
    _serial->write(command, 8);
    _serial->flush();

    Serial.print("发送XP出货命令: ");
    for (int i = 0; i < 8; i++) {
        Serial.printf("0x%02X ", command[i]);
    }
    Serial.println();

    // 设置出货状态为进行中
    _lastDispenseStatus = DISPENSE_STATUS_IN_PROGRESS;

    // 等待响应
    bool responseReceived = waitForResponse(timeout);

    return responseReceived;
}

bool SerialMotorController::sendXPRefundCommand(uint8_t refundAmount1, uint8_t refundAmount2, uint32_t timeout)
{
    uint8_t command[8]; // 帧头(2) + 命令(1) + 数据(2) + 加密(1) + 校验(1) + 帧尾(1)

    command[0] = XP_FRAME_HEADER1;  // 'X'
    command[1] = XP_FRAME_HEADER2;  // 'P'
    command[2] = XP_CMD_REFUND;     // 退币命令
    command[3] = refundAmount1;     // 退币马达1应出数量
    command[4] = refundAmount2;     // 退币马达2应出数量

    // 生成加密字节
    uint8_t encryptByte = generateXPEncryptByte();
    command[5] = encryptByte;

    // 计算校验和
    command[6] = calculateXPChecksum(command, 6);

    // 应用加密
    // applyXPEncryption(&command[3], 2, encryptByte); // 加密命令和数据字节

    // 帧尾
    command[7] = XP_FRAME_FOOTER;  // 'L'

    // 清空接收缓冲区
    while (_serial->available()) {
        _serial->read();
    }

    // 发送命令
    _serial->write(command, 8);
    _serial->flush();

    Serial.print("发送XP退币命令: ");
    for (int i = 0; i < 8; i++) {
        Serial.printf("0x%02X ", command[i]);
    }
    Serial.println();

    // 等待响应
    bool responseReceived = waitForResponse(timeout);

    return responseReceived;
}

bool SerialMotorController::sendXPResetCommand(uint32_t timeout)
{
    uint8_t command[8]; // 帧头(2) + 命令(1) + 数据(2) + 加密(1) + 校验(1) + 帧尾(1)

    command[0] = XP_FRAME_HEADER1;  // 'X'
    command[1] = XP_FRAME_HEADER2;  // 'P'
    command[2] = XP_CMD_RESET;      // 复位命令
    command[3] = 0x00;              // 数据字节1
    command[4] = 0x00;              // 数据字节2

    // 生成加密字节
    uint8_t encryptByte = generateXPEncryptByte();
    command[5] = encryptByte;

    // 计算校验和
    command[6] = calculateXPChecksum(command, 6);

    // 应用加密
    // applyXPEncryption(&command[3], 2, encryptByte); // 加密命令和数据字节

    // 帧尾
    command[7] = XP_FRAME_FOOTER;  // 'L'

    // 清空接收缓冲区
    while (_serial->available()) {
        _serial->read();
    }

    // 发送命令
    _serial->write(command, 8);
    _serial->flush();

    Serial.print("发送XP复位命令: ");
    for (int i = 0; i < 8; i++) {
        Serial.printf("0x%02X ", command[i]);
    }
    Serial.println();

    // 等待响应
    bool responseReceived = waitForResponse(timeout);

    return responseReceived;
}

void SerialMotorController::processXPResponse(uint8_t* data, uint8_t length)
{
    // 确保至少有8个字节
    if (length < 8) {
        Serial.println("XP响应长度错误");
        return;
    }

    // 解密数据 (与verifyXPChecksum方法中的解密逻辑相同)
    // uint8_t encryptByte = data[5];
    // uint8_t decryptedData[2];

    // 复制需要解密的数据
    // memcpy(decryptedData, &data[3], 2);

    // 解密
    // if ((encryptByte & 0x20) == 0) {
    //     // 取反解密
    //     for (int i = 0; i < 2; i++) {
    //         decryptedData[i] = ~decryptedData[i];
    //     }
    // } else {
    //     // 异或解密
    //     for (int i = 0; i < 2; i++) {
    //         decryptedData[i] ^= encryptByte;
    //     }
    // }

    // 解析命令和数据
    uint8_t command = data[2];
    uint8_t data1 = data[3];
    uint8_t data2 = data[4];

    Serial.printf("收到XP响应: 命令=0x%02X, 数据1=0x%02X, 数据2=0x%02X\n", command, data1, data2);

    // 根据命令类型处理
    switch (command) {
        case XP_CMD_HANDSHAKE: // 注意：XP_CMD_IDLE与XP_CMD_HANDSHAKE值相同(0x00)
            if (data1 == 0xE0 && data2 == 0xE0) {
                Serial.println("收到XP握手响应");
            } else {
                Serial.println("收到XP闲置反馈");
            }
            break;

        case XP_CMD_DISPENSE:
            Serial.printf("收到XP出货成功响应: 行=%d, 列=%d\n", data1, data2);
            _lastDispenseStatus = DISPENSE_STATUS_COMPLETE;
            break;

        case XP_CMD_DISPENSE_ERROR:
            Serial.printf("收到XP出货错误响应: 错误行=%d, 错误列=%d\n", data1, data2);
            _lastDispenseStatus = DISPENSE_STATUS_FAILED;
            break;

        case XP_CMD_REFUND_SUCCESS:
            Serial.printf("收到XP退币成功响应: 退币马达1=%d, 退币马达2=%d\n", data1, data2);
            break;

        case XP_CMD_REFUND_FAILED:
            Serial.println("收到XP退币失败响应");
            break;

        case XP_CMD_RESET:
            Serial.println("收到XP复位响应");
            break;

        default:
            Serial.printf("未知XP命令: 0x%02X\n", command);
            // 打印所有接收到的串口数据
            for (int i = 0; i < length; i++) {
                Serial.printf("0x%02X ", data[i]);
            }
            Serial.println();
            break;
    }
}

uint8_t SerialMotorController::generateXPEncryptByte()
{
    return 0;
    // 生成随机加密字节
    // return random(0, 256);
}

void SerialMotorController::applyXPEncryption(uint8_t* data, uint8_t length, uint8_t encryptByte)
{
    if ((encryptByte & 0x20) == 0) {
        // 取反加密
        for (int i = 0; i < length; i++) {
            data[i] = ~data[i];
        }
    } else {
        // 异或加密
        for (int i = 0; i < length; i++) {
            data[i] ^= encryptByte;
        }
    }
}

uint8_t SerialMotorController::calculateXPChecksum(uint8_t* data, uint8_t length)
{
    uint8_t checksum = 0;

    // 异或校验和
    for (int i = 0; i < length; i++) {
        checksum ^= data[i];
    }

    return checksum;
}

bool SerialMotorController::verifyXPChecksum(uint8_t* data, uint8_t length)
{
    // 确保至少有8个字节
    if (length < 8) {
        return false;
    }

    // 先解密数据
    uint8_t encryptByte = data[5];
    uint8_t decryptedData[2];

    // 复制需要解密的数据
    memcpy(decryptedData, &data[3], 2);

    // 解密
    if ((encryptByte & 0x20) == 0) {
        // 取反解密
        for (int i = 0; i < 2; i++) {
            decryptedData[i] = ~decryptedData[i];
        }
    } else {
        // 异或解密
        for (int i = 0; i < 2; i++) {
            decryptedData[i] ^= encryptByte;
        }
    }

    // 计算校验和 (使用解密后的数据)
    uint8_t calculatedChecksum = data[0] ^ data[1] ^ data[2]; // 帧头两个字节

    // 加上解密后的数据
    for (int i = 0; i < 2; i++) {
        calculatedChecksum ^= decryptedData[i];
    }

    // 加上加密字节
    calculatedChecksum ^= encryptByte;

    // 验证校验和
    return (calculatedChecksum == data[6]);
}
