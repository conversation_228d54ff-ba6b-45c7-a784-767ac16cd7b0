#ifndef CONNECTION_MANAGER_H
#define CONNECTION_MANAGER_H

#include <Arduino.h>
#include "HardwareNetworkManager.h"
#include "MQTTClient.h"
#include "config.h"

// 连接状态枚举
enum ConnectionState {
    STATE_DISCONNECTED = 0,    // 未连接
    STATE_CONNECTING_NETWORK,  // 正在连接网络
    STATE_NETWORK_CONNECTED,   // 网络已连接
    STATE_CONNECTING_MQTT,     // 正在连接MQTT
    STATE_MQTT_CONNECTED       // MQTT已连接
};

class ConnectionManager {
public:
    ConnectionManager(HardwareNetworkManager* networkManager, MQTTClient* mqttClient);

    // 初始化连接管理器
    bool begin();

    // 处理连接事件（需要在loop中调用）
    void handleEvents();

    // 获取当前连接状态
    ConnectionState getState();

    // 检查网络是否已连接
    bool isNetworkConnected();

    // 检查MQTT是否已连接
    bool isMqttConnected();

    // 强制重新连接网络
    void reconnectNetwork();

    // 强制重新连接MQTT
    void reconnectMqtt();

    // 断开所有连接
    void disconnectAll();

    // 设置连接状态变化回调
    typedef std::function<void(ConnectionState oldState, ConnectionState newState)> StateChangeCallback;
    void setStateChangeCallback(StateChangeCallback callback);

private:
    HardwareNetworkManager* _hardwareNetworkManager;
    MQTTClient* _mqttClient;

    // 当前连接状态
    ConnectionState _state;

    // 上次状态变化时间
    unsigned long _lastStateChangeTime;

    // 上次网络连接尝试时间
    unsigned long _lastNetworkConnectAttempt;

    // 上次MQTT连接尝试时间
    unsigned long _lastMqttConnectAttempt;

    // 网络重连间隔（毫秒）
    const unsigned long NETWORK_RECONNECT_INTERVAL = 5000;

    // MQTT重连间隔（毫秒）
    const unsigned long MQTT_RECONNECT_INTERVAL = 5000;

    // 连接超时时间（毫秒）
    const unsigned long CONNECTION_TIMEOUT = 30000;

    // 状态变化回调
    StateChangeCallback _stateChangeCallback;

    // 更新连接状态
    void setState(ConnectionState newState);

    // 检查并更新连接状态
    void updateConnectionState();

    // 处理网络连接
    void handleNetworkConnection();

    // 处理MQTT连接
    void handleMqttConnection();
};

#endif // CONNECTION_MANAGER_H
