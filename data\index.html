<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自助贩卖机管理系统</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>自助贩卖机管理系统</h1>
            <div class="status-bar">
                <span id="connection-status">连接状态: 未知</span>
                <span id="network-mode">网络模式: 未知</span>
                <span id="signal-strength">信号强度: 未知</span>
            </div>
        </header>

        <nav>
            <ul>
                <li><a href="#" data-page="dashboard" class="active">仪表盘</a></li>
                <li><a href="#" data-page="network">网络设置</a></li>
                <li><a href="#" data-page="mqtt">MQTT设置</a></li>
                <li><a href="#" data-page="device">设备控制</a></li>
                <li><a href="#" data-page="system">系统设置</a></li>
            </ul>
        </nav>

        <main>
            <!-- 仪表盘页面 -->
            <section id="dashboard" class="page active">
                <h2>系统状态</h2>
                <div class="status-grid">
                    <div class="status-card">
                        <h3>网络状态</h3>
                        <p>WiFi: <span id="wifi-status">未知</span></p>
                        <p>4G: <span id="cellular-status">未知</span></p>
                        <p>IP地址: <span id="ip-address">未知</span></p>
                    </div>
                    <div class="status-card">
                        <h3>MQTT状态</h3>
                        <p>连接状态: <span id="mqtt-status">未知</span></p>
                        <p>服务器: <span id="mqtt-server">未知</span></p>
                    </div>
                    <!-- <div class="status-card">
                        <h3>设备状态</h3>
                        <p>电机状态: <span id="motor-status">停止</span></p>
                        <p>红外状态: <span id="ir-status">未触发</span></p>
                    </div> -->
                    <div class="status-card">
                        <h3>系统信息</h3>
                        <p>固件版本: <span id="firmware-version">未知</span></p>
                        <p>运行时间: <span id="uptime">未知</span></p>
                        <p>可用内存: <span id="free-heap">未知</span></p>
                        <p>MAC地址: <span id="mac-address">未知</span></p>
                    </div>
                </div>

                <h2>位置信息</h2>
                <div class="location-card">
                    <p>纬度: <span id="latitude">未知</span></p>
                    <p>经度: <span id="longitude">未知</span></p>
                    <p>最后更新: <span id="location-update">未知</span></p>
                </div>

                <div class="action-buttons">
                    <button id="refresh-status">刷新状态</button>
                </div>
            </section>

            <!-- 网络设置页面 -->
            <section id="network" class="page">
                <h2>网络设置</h2>
                <form id="network-form">
                    <div class="form-group">
                        <label>网络模式:</label>
                        <select id="network-mode-select">
                            <option value="0">WiFi</option>
                            <option value="1">4G</option>
                        </select>
                    </div>

                    <div class="card">
                        <h3>WiFi设置</h3>
                        <div class="form-group">
                            <label for="wifi-enabled">启用WiFi:</label>
                            <input type="checkbox" id="wifi-enabled">
                        </div>
                        <div class="form-group">
                            <label for="wifi-ssid">WiFi名称:</label>
                            <input type="text" id="wifi-ssid">
                        </div>
                        <div class="form-group">
                            <label for="wifi-password">WiFi密码:</label>
                            <input type="text" id="wifi-password">
                        </div>
                    </div>

                    <div class="card">
                        <h3>4G设置</h3>
                        <div class="form-group">
                            <label for="cellular-enabled">启用4G:</label>
                            <input type="checkbox" id="cellular-enabled">
                        </div>
                        <div class="form-group">
                            <label for="cellular-apn">APN:</label>
                            <input type="text" id="cellular-apn">
                        </div>
                        <div class="form-group">
                            <label for="cellular-user">用户名:</label>
                            <input type="text" id="cellular-user">
                        </div>
                        <div class="form-group">
                            <label for="cellular-password">密码:</label>
                            <input type="text" id="cellular-password">
                        </div>
                    </div>

                    <div class="action-buttons">
                        <button type="submit">保存设置</button>
                    </div>
                </form>
            </section>

            <!-- MQTT设置页面 -->
            <section id="mqtt" class="page">
                <h2>MQTT设置</h2>
                <form id="mqtt-form">
                    <div class="card">
                        <h3>服务器设置</h3>
                        <div class="form-group">
                            <label for="mqtt-server-input">服务器地址:</label>
                            <input type="text" id="mqtt-server-input">
                        </div>
                        <div class="form-group">
                            <label for="mqtt-port">端口:</label>
                            <input type="number" id="mqtt-port">
                        </div>
                        <div class="form-group">
                            <label for="mqtt-user">用户名:</label>
                            <input type="text" id="mqtt-user">
                        </div>
                        <div class="form-group">
                            <label for="mqtt-password">密码:</label>
                            <input type="text" id="mqtt-password">
                        </div>
                        <div class="form-group">
                            <label for="mqtt-client-id">客户端ID (MAC地址):</label>
                            <input type="text" id="mqtt-client-id" readonly>
                            <small class="form-text">客户端ID自动使用设备MAC地址，无需手动设置</small>
                        </div>
                    </div>

                    <div class="card">
                        <h3>主题设置</h3>
                        <div class="form-group">
                            <label for="mqtt-topic-status">发布主题:</label>
                            <input type="text" id="mqtt-topic-status">
                        </div>
                        <div class="form-group">
                            <label for="mqtt-topic-command">订阅主题:</label>
                            <input type="text" id="mqtt-topic-command">
                        </div>
                        <!-- <div class="form-group">
                            <label for="mqtt-topic-location">位置主题:</label>
                            <input type="text" id="mqtt-topic-location">
                        </div> -->
                        <!-- <div class="form-group">
                            <label for="mqtt-update-interval">位置上报间隔(秒):</label>
                            <input type="number" id="mqtt-update-interval">
                        </div> -->
                    </div>

                    <div class="action-buttons">
                        <button type="submit">保存设置</button>
                    </div>
                </form>
            </section>

            <!-- 设备控制页面 -->
            <section id="device" class="page">
                <h2>设备控制</h2>
                <!-- <div class="card">
                    <h3>电机控制</h3>
                    <div class="form-group">
                        <label for="motor-address">电机地址:</label>
                        <input type="number" id="motor-address" value="1" min="1" max="255">
                    </div>
                    <div class="form-group">
                        <label for="motor-command">命令:</label>
                        <select id="motor-command">
                            <option value="0">停止</option>
                            <option value="1">正转</option>
                            <option value="2">反转</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="motor-duration">持续时间(毫秒):</label>
                        <input type="number" id="motor-duration" value="0" min="0">
                    </div>
                    <div class="action-buttons">
                        <button id="control-motor">控制电机</button>
                    </div>
                </div> -->

                <div class="card">
                    <h3>出货控制</h3>
                    <div class="form-group">
                        <label for="dispense-row">行号:</label>
                        <input type="number" id="dispense-row" value="1" min="1" max="20">
                    </div>
                    <div class="form-group">
                        <label for="dispense-column">列号:</label>
                        <input type="number" id="dispense-column" value="1" min="1" max="20">
                    </div>
                    <div class="action-buttons">
                        <button id="dispense-product">出货</button>
                    </div>
                    <div id="dispense-result" class="result-box">
                        <p>出货结果: <span id="dispense-success">未执行</span></p>
                        <p>红外检测: <span id="dispense-ir">未触发</span></p>
                    </div>
                </div>

                <div class="card">
                    <h3>传感器状态</h3>
                    <div class="sensor-status">
                        <p>红外传感器: <span id="ir-sensor1-status">未触发</span></p>
                        <p>人体红外传感器1: <span id="pir-sensor-status">未触发</span></p>
                        <p>人体红外传感器2: <span id="ir-sensor2-status">未触发</span></p>
                        <p>人体红外传感器3: <span id="ir-sensor3-status">未触发</span></p>
                    </div>
                </div>

                <div class="card">
                    <h3>继电器控制</h3>
                    <div class="relay-controls">
                        <div class="relay-row">
                            <span>继电器1:</span>
                            <span id="relay1-status">关闭</span>
                            <button id="relay1-on">开启</button>
                            <button id="relay1-off">关闭</button>
                        </div>
                        <div class="relay-row">
                            <span>继电器2:</span>
                            <span id="relay2-status">关闭</span>
                            <button id="relay2-on">开启</button>
                            <button id="relay2-off">关闭</button>
                        </div>
                        <div class="relay-row">
                            <span>继电器3:</span>
                            <span id="relay3-status">关闭</span>
                            <button id="relay3-on">开启</button>
                            <button id="relay3-off">关闭</button>
                        </div>
                        <div class="relay-row">
                            <span>继电器4:</span>
                            <span id="relay4-status">关闭</span>
                            <button id="relay4-on">开启</button>
                            <button id="relay4-off">关闭</button>
                        </div>
                        <div class="relay-row">
                            <span>继电器5:</span>
                            <span id="relay5-status">关闭</span>
                            <button id="relay5-on">开启</button>
                            <button id="relay5-off">关闭</button>
                        </div>
                        <div class="relay-all-controls">
                            <button id="all-relays-on">全部开启</button>
                            <button id="all-relays-off">全部关闭</button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 系统设置页面 -->
            <section id="system" class="page">
                <h2>系统设置</h2>
                <div class="card">
                    <h3>设备信息</h3>
                    <form id="device-form">
                        <div class="form-group">
                            <label for="device-name">项目名称:</label>
                            <input type="text" id="device-name">
                        </div>
                        <div class="action-buttons">
                            <button type="submit">保存设置</button>
                        </div>
                    </form>
                </div>

                <div class="card">
                    <h3>API接口配置</h3>
                    <form id="api-form">
                        <div class="form-group">
                            <label for="api-goods-out">出货接口 (goodsOut):</label>
                            <!-- <input type="text" id="api-goods-out" placeholder="http://xmc.xmtxrj.com:8082/apiSaasWrgj/memberOrderGoodsOut/updateMemberOrderGoodsOutStatus_barCode"> -->
                            <textarea type="text" id="api-goods-out" placeholder="http://xmc.xmtxrj.com:8082/apiSaasWrgj/memberOrderGoodsOut/updateMemberOrderGoodsOutStatus_barCode"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="api-cabinet-bin-test">货道测试接口 (cabinetBinTest):</label>
                            <!-- <input type="text" id="api-cabinet-bin-test" placeholder="http://xmc.xmtxrj.com:8082/apiSaasWrgj/cabinetBinTest/updateCabinetcabinetBinTestStatus"> -->
                            <textarea type="text" id="api-cabinet-bin-test" placeholder="http://xmc.xmtxrj.com:8082/apiSaasWrgj/cabinetBinTest/updateCabinetcabinetBinTestStatus"></textarea>
                        </div>
                        <!-- <div class="form-group">
                            <label for="api-get-device-params">设备参数接口 (getDeviceParams):</label>
                            <input type="text" id="api-get-device-params" placeholder="http://xmc.xmtxrj.com:8082/cabinetInfo/deviceParams">
                        </div> -->
                        <div class="action-buttons">
                            <button type="submit">保存API设置</button>
                        </div>
                    </form>
                </div>

                <div class="card">
                    <h3>固件升级</h3>
                    <div class="firmware-info">
                        <p>当前固件版本: <span id="current-firmware-version">未知</span></p>
                        <p>设备可用空间: <span id="available-space">未知</span></p>
                    </div>
                    <form id="ota-form" enctype="multipart/form-data">
                        <div class="form-group">
                            <label for="firmware-file">固件文件:</label>
                            <input type="file" id="firmware-file" accept=".bin">
                            <small class="form-text">请选择适用于ESP32的.bin固件文件</small>
                        </div>
                        <div class="progress-container">
                            <div class="progress-bar">
                                <div id="upload-progress" class="progress"></div>
                            </div>
                            <div id="upload-status">准备就绪</div>
                            <div id="upload-percentage">0%</div>
                        </div>
                        <div class="action-buttons">
                            <button type="submit" id="upload-firmware-btn">上传固件</button>
                            <button type="button" id="cancel-upload-btn" style="display:none;">取消上传</button>
                        </div>
                    </form>
                    <div class="firmware-notes">
                        <p><strong>注意事项:</strong></p>
                        <ul>
                            <li>升级过程中请勿断电或刷新页面</li>
                            <li>升级完成后设备将自动重启</li>
                            <li>如升级失败，设备将保持当前固件版本</li>
                        </ul>
                    </div>
                </div>

                <div class="card">
                    <h3>系统操作</h3>
                    <div class="action-buttons">
                        <button id="restart-system">重启系统</button>
                    </div>
                </div>
            </section>
        </main>

        <footer>
            <p>自助贩卖机管理系统 &copy; 2025</p>
        </footer>
    </div>

    <div id="toast" class="toast"></div>

    <script src="app.js"></script>
</body>
</html>
