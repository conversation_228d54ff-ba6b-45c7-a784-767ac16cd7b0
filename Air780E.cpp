#include "Air780E.h"

Air780E::Air780E() : _isConnected(false), _lastCommandTime(0),
                     _mqttConfigured(false), _mqttConnected(false), _mqttCallback(NULL),
                     _lastMqttStatusCheck(0) {
    // 初始化MQTT相关变量
    memset(_mqttClientId, 0, sizeof(_mqttClientId));
    memset(_mqttServer, 0, sizeof(_mqttServer));
    _mqttPort = 1883;
    memset(_mqttUsername, 0, sizeof(_mqttUsername));
    memset(_mqttPassword, 0, sizeof(_mqttPassword));
}

bool Air780E::begin() {
    // 初始化串口
    AIR780E_SERIAL.begin(AIR780E_BAUD, SERIAL_8N1, PIN_AIR780E_RX, PIN_AIR780E_TX);

    // 配置电源控制引脚
    pinMode(PIN_AIR780E_PWRKEY, OUTPUT);

    restart();

    // 测试AT命令
    String response = sendATCommand("AT");
    if (response.indexOf("OK") == -1) {
        response = sendATCommand("AT");
        if (response.indexOf("OK") == -1) {
            Serial.println("模块未响应");
            return false;
        }
    }

    // 关闭回显
    sendATCommand("ATE0");

    // 检查SIM卡状态
    response = sendATCommand("AT+CPIN?");
    if (response.indexOf("READY") == -1) {
        Serial.println("SIM卡未就绪");
        restart();
        response = sendATCommand("AT+CPIN?");
        if (response.indexOf("READY") == -1)
        {
            Serial.println("SIM卡未就绪");
            return false;
        }
    }

    // 等待网络注册
    int i;
    for (i = 0; i < 60; i++) {
        response = sendATCommand("AT+CREG?");
        if (response.indexOf("+CREG: 0,1") != -1) {
            Serial.println("网络注册成功");
            break;
        }
        delay(1000);
        if (i == 59)
        {
            Serial.println("网络注册失败,重新启动模块");
            i = 0;
            restart();
        }
    }

    // 检查网络是否连接
    if (!isConnected()) {
        Serial.println("网络未连接");
        return false;
    }
    
    return true;
}

bool Air780E::connect(const char* apn, const char* user, const char* password) {
    // 设置APN(公网SIM卡，不需要用户主动设置APN，软件自动去网络端查询APN进行设置)
    String apnCmd = "AT+CGDCONT=1,\"IP\",\"";
    apnCmd += apn;
    apnCmd += "\"";
    sendATCommand(apnCmd.c_str());

    // 激活PDP上下文
    String response = sendATCommand("AT+CGACT=1,1", 5000);
    if (response.indexOf("OK") == -1) {
        Serial.println("激活PDP上下文失败");
        return false;
    }

    // 等待获取IP地址
    // for (int i = 0; i < 10; i++) {
    //     String ip = getIPAddress();
    //     if (ip.length() > 0 && ip != "0.0.0.0") {
    //         _isConnected = true;
    //         Serial.println("获取IP地址成功: " + ip);
    //         return true;
    //     }
    //     delay(1000);
    // }

    // Serial.println("获取IP地址失败");

    return isConnected();
}

bool Air780E::disconnect() {
    // 停用PDP上下文
    // String response = sendATCommand("AT+CGACT=0,1");
    // if (response.indexOf("OK") != -1) {
    //     _isConnected = false;
    //     return true;
    // }
    // return false;

    return true;
}

bool Air780E::isConnected() {
    // 检查PDP上下文状态
    String response = sendATCommand("AT+CGATT?");
    if (response.indexOf("+CGATT: 1") != -1)
    {
        _isConnected = true;
        return true;
    }
    _isConnected = false;
    return false;
}

uint8_t Air780E::getSignalStrength() {
    String response = sendATCommand("AT+CSQ");
    int csqPos = response.indexOf("+CSQ: ");

    if (csqPos != -1) {
        int commaPos = response.indexOf(",", csqPos);
        if (commaPos != -1) {
            int csq = response.substring(csqPos + 6, commaPos).toInt();
            if (csq == 99) {
                return 0;  // 无信号
            } else {
                // 将CSQ值(0-31)转换为百分比(0-100)
                return map(csq, 0, 31, 0, 100);
            }
        }
    }

    return 0;
}

// GPS相关方法

// 开启GNSS电源
bool Air780E::gpsOpen() {
    // 检查GPS是否已经开启
    if (isGpsOpen()) {
        Serial.println("GPS已经开启");
        return true;
    }

    // 开启GNSS电源
    String response = sendATCommand("AT+CGNSPWR=1");
    if (response.indexOf("OK") == -1) {
        Serial.println("GPS电源开启失败");
        return false;
    }

    Serial.println("GPS电源开启成功");

    return true;
}

// 关闭GNSS电源
bool Air780E::gpsClose() {
    // 关闭GNSS电源
    String response = sendATCommand("AT+CGNSPWR=0");
    if (response.indexOf("OK") == -1) {
        Serial.println("GPS电源关闭失败");
        return false;
    }

    Serial.println("GPS电源关闭成功");
    Serial.println("==============================\n");
    return true;
}

// 检查GPS是否已开启
bool Air780E::isGpsOpen() {
    String response = sendATCommand("AT+CGNSPWR?");
    return (response.indexOf("+CGNSPWR: 1") != -1);
}

// 获取GPS状态
int Air780E::getGpsStatus() {
    String response = sendATCommand("AT+CGNSINF", 2000);

    // +CGNSINF: <GNSS run status>,<Fix status>,<UTC date & time>,<Latitude>,<Longitude>,<MSL altitude>,<Speed over ground>,<Course over ground>,<Fix mode>,<Reserved1>,<HDOP>,<PDOP>,<VDOP>,<Reserved2>,<GNSS satellites in view>,<GNSS satellites used>,<Reserved3>,<C/N0 max>,<HPA>,<VPA>
    // 其中：<Fix status> 0=未定位，1=已定位

    int startPos = response.indexOf("+CGNSINF: ");
    if (startPos != -1) {
        startPos += 10; // 跳过"+CGNSINF: "

        // 获取GNSS运行状态
        int commaPos = response.indexOf(",", startPos);
        int runStatus = response.substring(startPos, commaPos).toInt();

        // 获取定位状态
        startPos = commaPos + 1;
        commaPos = response.indexOf(",", startPos);
        int fixStatus = response.substring(startPos, commaPos).toInt();

        // 返回定位状态
        return fixStatus;
    }

    return 0; // 默认未定位
}

// 获取原始GPS信息
String Air780E::getGpsInfo() {
    return sendATCommand("AT+CGNSINF", 2000);
}

// 获取位置信息
bool Air780E::getLocation(float &latitude, float &longitude) {
    // 检查GPS是否开启
    if (!isGpsOpen()) {
        Serial.println("GPS未开启，尝试开启GPS");
        if (!gpsOpen()) {
            Serial.println("GPS开启失败，无法获取位置");
            return false;
        }
        // 等待GPS启动
        delay(2000);
    }

    // 获取GPS信息
    String response = sendATCommand("AT+CGNSINF", 2000);
    Serial.printf("GPS信息: %s\n", response.c_str());

    // +CGNSINF: <GNSS run status>,<Fix status>,<UTC date & time>,<Latitude>,<Longitude>,<MSL altitude>,<Speed over ground>,<Course over ground>,<Fix mode>,<Reserved1>,<HDOP>,<PDOP>,<VDOP>,<Reserved2>,<GNSS satellites in view>,<GNSS satellites used>,<Reserved3>,<C/N0 max>,<HPA>,<VPA>

    int startPos = response.indexOf("+CGNSINF: ");
    if (startPos != -1) {
        startPos += 10; // 跳过"+CGNSINF: "

        // 获取GNSS运行状态
        int commaPos = response.indexOf(",", startPos);
        int runStatus = response.substring(startPos, commaPos).toInt();

        // 获取定位状态
        startPos = commaPos + 1;
        commaPos = response.indexOf(",", startPos);
        int fixStatus = response.substring(startPos, commaPos).toInt();

        // 如果未定位，返回失败
        if (fixStatus != 1) {
            Serial.println("GPS未定位，无法获取位置");
            return false;
        }

        // 跳过UTC时间
        startPos = commaPos + 1;
        commaPos = response.indexOf(",", startPos);

        // 获取纬度
        startPos = commaPos + 1;
        commaPos = response.indexOf(",", startPos);
        String latStr = response.substring(startPos, commaPos);

        // 获取经度
        startPos = commaPos + 1;
        commaPos = response.indexOf(",", startPos);
        String lonStr = response.substring(startPos, commaPos);

        // 转换为浮点数
        if (latStr.length() > 0 && lonStr.length() > 0) {
            latitude = latStr.toFloat();
            longitude = lonStr.toFloat();

            Serial.printf("GPS定位成功: 纬度=%.6f, 经度=%.6f\n", latitude, longitude);
            return true;
        }
    }

    Serial.println("GPS信息解析失败");
    return false;
}

String Air780E:: sendATCommand(const char* command, unsigned long timeout) {
    // 确保命令之间有足够的间隔
    unsigned long currentTime = millis();
    if (currentTime - _lastCommandTime < 100) {
        delay(100);
    }
    _lastCommandTime = millis();

    // 清空接收缓冲区，但保留MQTT消息
    while (AIR780E_SERIAL.available()) {
        String line = AIR780E_SERIAL.readStringUntil('\n');

        // 检查是否是MQTT消息，如果是则等待300ms让消息接收完整
        // 解析MQTT消息
        // 格式: +MSUB: "<topic>",<len> byte,<payload>
        // 例如: +MSUB: "ESP32PI/DRIVER/MAC_ADDRESS",47 byte,{ "command": "dispense", "channel": 1 }
        if (line.indexOf("+MSUB:") != -1) {
            // 延时300ms等待消息接收完整
            delay(300);

            // 读取所有可用数据
            String mqttBuffer = line;
            while (AIR780E_SERIAL.available()) {
                String additionalData = AIR780E_SERIAL.readStringUntil('\n');
                mqttBuffer += additionalData;
            }
            Serial.println("收到MQTT完整消息: " + mqttBuffer);
            // 解析主题
            int firstQuote = mqttBuffer.indexOf('"');
            int secondQuote = mqttBuffer.indexOf('"', firstQuote + 1);

            if (firstQuote != -1 && secondQuote != -1) {
                String topic = mqttBuffer.substring(firstQuote + 1, secondQuote);

                //继续解析长度
                int commaPos = mqttBuffer.indexOf(",", secondQuote + 3);

                // 解析JSON负载，直接把整个消息作为JSON负载
                String jsonPayload = mqttBuffer.substring(commaPos + 1);

                // 调用回调函数
                if (_mqttCallback != NULL) {
                    _mqttCallback(topic.c_str(), jsonPayload.c_str());
                }
            }
        }
        else {
            String trimmedData = line;
            trimmedData.trim();

            if (trimmedData.length() > 0) {
                // 只打印非空行
                Serial.println("收到串口数据: " + line);
            }
        }
    }

    // 发送命令
    AIR780E_SERIAL.println(command);

    // 检查是否是需要特殊处理的命令
    bool isSpecialCommand = false;
    bool needExtraWait = false;

    // 检查是否是NTP同步命令
    if (strncmp(command, "AT+CNTP", 7) == 0 && strlen(command) == 7) {
        isSpecialCommand = true;
        needExtraWait = true;
        Serial.println("检测到NTP同步命令，将等待延迟响应");
    }

    // 读取响应
    String response = "";
    unsigned long startTime = millis();
    bool receivedOK = false;

    while (millis() - startTime < timeout) {
        if (AIR780E_SERIAL.available()) {
            char c = AIR780E_SERIAL.read();
            response += c;

            // 检查是否接收到OK响应
            if (response.endsWith("OK\r\n")) {
                receivedOK = true;

                // 如果不是特殊命令，接收到OK就可以返回了
                if (!isSpecialCommand) {
                    break;
                }

                // 如果是特殊命令，接收到OK后还需要继续等待
                if (isSpecialCommand && !needExtraWait) {
                    break;
                }
            }

            // 检查是否接收到ERROR响应
            if (response.endsWith("ERROR\r\n")) {
                break;
            }

            // 检查是否接收到>提示符
            if (response.endsWith("> ")) {
                break;
            }

            // 对于特殊命令，检查是否接收到特定的响应
            if (isSpecialCommand) {
                // 检查NTP同步命令的响应
                if (strncmp(command, "AT+CNTP", 7) == 0 && response.indexOf("+CNTP: 1") != -1) {
                    Serial.println("收到NTP同步成功响应");
                    needExtraWait = false;
                    break;
                }
            }
        }

        // 如果已经接收到OK，并且是需要额外等待的特殊命令
        // 等待一段时间后再检查是否有新数据
        if (receivedOK && needExtraWait) {
            // 如果300ms内没有新数据，就认为响应已经完成
            if (millis() - startTime > 300 && !AIR780E_SERIAL.available()) {
                Serial.println("特殊命令等待300ms后无新数据，返回响应");
                break;
            }
        }
    }

    // 如果是特殊命令，并且接收到了OK但没有接收到特定响应
    // 再等待一段时间，看是否能接收到特定响应
    if (isSpecialCommand && receivedOK && needExtraWait) {
        Serial.println("特殊命令接收到OK但未接收到特定响应，继续等待");

        // 再等待最多500ms
        unsigned long extraStartTime = millis();
        while (millis() - extraStartTime < 500) {
            if (AIR780E_SERIAL.available()) {
                char c = AIR780E_SERIAL.read();
                response += c;

                // 检查NTP同步命令的响应
                if (strncmp(command, "AT+CNTP", 7) == 0 && response.indexOf("+CNTP: 1") != -1) {
                    Serial.println("额外等待期间收到NTP同步成功响应");
                    break;
                }
            }

            // 如果100ms内没有新数据，就认为响应已经完成
            if (millis() - extraStartTime > 100 && !AIR780E_SERIAL.available()) {
                break;
            }
        }
    }

    return response;
}

bool Air780E::restart() {
    powerOn();
    delay(5000);

    return true;
}

String Air780E::getIMEI() {
    String response = sendATCommand("AT+CGSN");
    // 解析IMEI
    String imei = "";
    int start = response.indexOf("\r\n");
    if (start != -1) {
        int end = response.indexOf("\r\n", start + 2);
        if (end != -1) {
            imei = response.substring(start + 2, end);
        }
    }
    return imei;
}

String Air780E::getICCID() {
    String response = sendATCommand("AT+CCID");
    // 解析IMEI
    String ccid = "";
    int start = response.indexOf("\r\n");
    if (start != -1) {
        int end = response.indexOf("\r\n", start + 2);
        if (end != -1) {
            ccid = response.substring(start + 2, end);
        }
    }
    return ccid;
}

String Air780E::getOperator() {
    String response = sendATCommand("AT+COPS?");
    String result = parseResponse(response, "+COPS: ");

    // 解析运营商名称
    int startPos = result.lastIndexOf(",\"");
    int endPos = result.lastIndexOf("\"");

    if (startPos != -1 && endPos != -1 && startPos < endPos) {
        return result.substring(startPos + 2, endPos);
    }

    return "";
}

String Air780E::getIPAddress() {
    String response = sendATCommand("AT+CGPADDR=1");
    String result = parseResponse(response, "+CGPADDR: 1,");

    // 移除引号
    result.replace("\"", "");

    return result;
}

bool Air780E::waitForResponse(const char* expected, unsigned long timeout) {
    String response = "";
    unsigned long startTime = millis();

    while (millis() - startTime < timeout) {
        if (AIR780E_SERIAL.available()) {
            char c = AIR780E_SERIAL.read();
            response += c;

            if (response.indexOf(expected) != -1) {
                return true;
            }
        }
    }

    return false;
}

String Air780E::parseResponse(const String &response, const char* prefix) {
    int prefixPos = response.indexOf(prefix);
    if (prefixPos != -1) {
        int start = prefixPos + strlen(prefix);
        int end = response.indexOf("\r\n", start);
        if (end != -1) {
            return response.substring(start, end);
        } else {
            return response.substring(start);
        }
    }
    return "";
}

void Air780E::powerOn() {
    // 合宙Air780E的PWRKEY引脚高电平复位，高电平1秒复位
    digitalWrite(PIN_AIR780E_PWRKEY, HIGH);
    delay(1300);
    digitalWrite(PIN_AIR780E_PWRKEY, LOW);
}

void Air780E::powerOff() {
    // 合宙Air780E的PWRKEY引脚需要拉低至少2000ms来关机
    // digitalWrite(PIN_AIR780E_PWRKEY, LOW);
    // delay(2500);
    // digitalWrite(PIN_AIR780E_PWRKEY, HIGH);
}

// MQTT相关方法实现

// 设置MQTT回调函数
void Air780E::setMqttCallback(MqttCallback callback) {
    _mqttCallback = callback;
}

// 配置MQTT客户端
bool Air780E::mqttConfig(const char* clientId, const char* server, uint16_t port, const char* username, const char* password) {
    // 先清空之前的配置
    memset(_mqttClientId, 0, sizeof(_mqttClientId));
    memset(_mqttServer, 0, sizeof(_mqttServer));
    memset(_mqttUsername, 0, sizeof(_mqttUsername));
    memset(_mqttPassword, 0, sizeof(_mqttPassword));

    // 保存MQTT配置
    strncpy(_mqttClientId, clientId, sizeof(_mqttClientId) - 1);
    strncpy(_mqttServer, server, sizeof(_mqttServer) - 1);
    _mqttPort = port;
    strncpy(_mqttUsername, username, sizeof(_mqttUsername) - 1);
    strncpy(_mqttPassword, password, sizeof(_mqttPassword) - 1);

    // 打印配置信息，确认数据已正确保存
    Serial.printf("MQTT配置保存: 客户端ID='%s', 服务器='%s:%d'\n", _mqttClientId, _mqttServer, _mqttPort);

    // 打印MQTT配置信息
    Serial.println("\n===== MQTT配置 =====\n");
    Serial.printf("MQTT服务器: %s:%d\n", _mqttServer, _mqttPort);
    Serial.printf("MQTT客户端ID: %s\n", _mqttClientId);
    if (strlen(_mqttUsername) > 0) {
        Serial.printf("MQTT用户名: %s\n", _mqttUsername);
        Serial.println("MQTT密码: ******");
    } else {
        Serial.println("MQTT不使用认证");
    }

    // 配置MQTT参数
    String cmd = "AT+MCONFIG=";
    cmd += _mqttClientId;

    // 如果有用户名和密码，添加到命令中
    if (strlen(_mqttUsername) > 0 && strlen(_mqttPassword) > 0) {
        cmd += ",";
        cmd += _mqttUsername;
        cmd += ",";
        cmd += _mqttPassword;
    }

    // <will_qos>,<will_retain>,<will_topic>,<will_message>
    // cmd += ",0,0,will_topic,will_message";

    Serial.println("MQTT配置命令: " + cmd);

    String response = sendATCommand(cmd.c_str());
    if (response.indexOf("OK") == -1) {
        Serial.println("MQTT参数配置失败");
        return false;
    }

    _mqttConfigured = true;
    Serial.println("MQTT配置成功");
    Serial.println("==============================\n");

    return true;
}

// 连接MQTT服务器
bool Air780E::mqttConnect() {
    if (!_mqttConfigured) {
        Serial.println("MQTT连接失败: 未配置");
        return false;
    }

    // 检查网络连接
    if (!isConnected()) {
        Serial.println("MQTT连接失败: 网络未连接");
        return false;
    }

    // 如果已经连接，先断开但不等待
    if (_mqttConnected) {
        mqttDisconnect();
        delay(1000);
    }

    // 1. 创建MQTT的TCP连接AT+MIPSTART=<svraddr>,<port>
    String cmd = "AT+MIPSTART=";
    cmd += _mqttServer;
    cmd += ",";
    cmd += _mqttPort;
    String response = sendATCommand(cmd.c_str(), 2000); // 减少超时时间
    if (response.indexOf("OK") == -1)
    {
        Serial.println("创建MQTT的TCP连接失败");
        return false;
    }

    delay(2000);

    // 2. MQTT客户端向服务器请求会话连接AT+MCONNECT=<clean_session>,<keepalive>
    cmd = "AT+MCONNECT=";
    cmd += "0";
    cmd += ",";
    cmd += "300";

    response = sendATCommand(cmd.c_str(), 2000); // 减少超时时间
    if (response.indexOf("OK") == -1)
    {
        Serial.println("MQTT客户端向服务器请求会话连接失败");
        return false;
    }

    delay(2000);

    // 设置连接标志
    _mqttConnected = true;
    Serial.println("MQTT连接命令已发送");

    return true;
}

// 断开MQTT连接
bool Air780E::mqttDisconnect() {
    if (!_mqttConnected) {
        return true;
    }

    String response = sendATCommand("AT+MDISCONNECT", 1000);
    if (response.indexOf("OK") == -1) {
        Serial.println("MQTT断开连接失败");
        return false;
    }

    _mqttConnected = false;
    Serial.println("MQTT断开连接成功");

    return true;
}

// 检查MQTT连接状态
bool Air780E::mqttIsConnected() {
    // 3.查询MQTT连接状态AT+MQTTSTATU 返回+MQTTSTATU :<state> OK
    // MQTT连接状态	0	离线  1 已经登陆认证过，可以PUB数据了 2 还没认证，需要发送MCONNECT命令

    // 强制检查连接状态，不使用缓存
    bool forceCheck = false;

    // 每2秒检查一次连接状态，避免频繁发送AT命令
    unsigned long currentMillis = millis();
    if (!forceCheck && currentMillis - _lastMqttStatusCheck < 2000) {
        return _mqttConnected;
    }

    _lastMqttStatusCheck = currentMillis;

    // 检查MQTT连接状态
    String response = sendATCommand("AT+MQTTSTATU");

    // 检查响应是否包含状态1（已连接）
    if (response.indexOf("+MQTTSTATU :1") != -1) {
        if (!_mqttConnected) {
            _mqttConnected = true;
            Serial.println("MQTT连接状态变化: 已连接");
        }
        return true;
    }
    // 检查响应是否包含状态2（未认证）
    else if (response.indexOf("+MQTTSTATU :2") != -1) {
        Serial.println("MQTT连接状态: 未认证，需要发送MCONNECT命令");
        _mqttConnected = false;
        return false;
    }
    // 检查响应是否包含状态0（离线）
    else if (response.indexOf("+MQTTSTATU :0") != -1) {
        if (_mqttConnected) {
            _mqttConnected = false;
            Serial.println("MQTT连接状态变化: 已断开");
        }
        return false;
    }
    // 响应不包含有效状态
    else {
        Serial.println("MQTT状态查询失败，响应: " + response);
        // 保持当前状态不变
        return _mqttConnected;
    }
}

// 发布MQTT消息
bool Air780E::mqttPublish(const char* topic, const char* payload, uint8_t qos, bool retained) {
    if (!_mqttConnected) {
        Serial.println("MQTT发布失败: 未连接");
        return false;
    }

    // 处理JSON字符串中的特殊字符，按照Air780E模块的要求进行转义
    String escapedPayload = payload;

    // 打印原始负载
    // Serial.println("原始MQTT负载: " + String(payload));

    // 先处理反斜杠，避免后续替换出现问题
    escapedPayload.replace("\\", "\\5C");
    // 处理双引号
    escapedPayload.replace("\"", "\\22");
    // 处理回车符
    escapedPayload.replace("\r", "\\0D");
    // 处理换行符
    escapedPayload.replace("\n", "\\0A");

    // 打印转义后的负载
    // Serial.println("转义后MQTT负载: " + escapedPayload);

    // 构造发布命令
    String cmd = "AT+MPUB=\"";
    cmd += topic;
    cmd += "\",";
    cmd += qos;
    cmd += ",";
    cmd += retained ? 1 : 0;
    cmd += ",\"";
    cmd += escapedPayload;
    cmd += "\"";

    Serial.println("MQTT发布命令: " + cmd);

    String response = sendATCommand(cmd.c_str(), 2000);
    if (response.indexOf("OK") == -1) {
        Serial.printf("MQTT发布失败: %s\n", topic);
        return false;
    }

    Serial.printf("MQTT发布成功: %s\n", topic);
    return true;
}

// 订阅MQTT主题 - 非阻塞版本
bool Air780E::mqttSubscribe(const char* topic, uint8_t qos) {
    if (!_mqttConnected) {
        Serial.println("MQTT订阅失败: 未连接");
        return false;
    }

    // 构造订阅命令
    String cmd = "AT+MSUB=";
    cmd += topic;
    cmd += ",";
    cmd += qos;

    // 减少超时时间，使操作更快速
    String response = sendATCommand(cmd.c_str(), 1000);
    if (response.indexOf("OK") == -1) {
        Serial.printf("MQTT订阅失败: %s\n", topic);
        return false;
    }

    Serial.printf("MQTT订阅成功: %s\n", topic);
    return true;
}

// 取消订阅MQTT主题
bool Air780E::mqttUnsubscribe(const char* topic) {
    if (!_mqttConnected) {
        Serial.println("MQTT取消订阅失败: 未连接");
        return false;
    }

    // 构造取消订阅命令
    String cmd = "AT+MUNSUB=\"";
    cmd += topic;
    cmd += "\"";

    String response = sendATCommand(cmd.c_str());
    if (response.indexOf("OK") == -1) {
        Serial.printf("MQTT取消订阅失败: %s\n", topic);
        return false;
    }

    Serial.printf("MQTT取消订阅成功: %s\n", topic);
    return true;
}

// 处理MQTT事件
void Air780E::mqttLoop() {
    // 此函数保留但不再使用
    // 所有MQTT消息处理都在sendATCommand函数中完成
}

// 发送HTTP GET请求
String Air780E::httpGet(const char* url, unsigned long timeout) {
    if (!isConnected()) {
        Serial.println("HTTP GET失败: 网络未连接");
        return "";
    }

    // 检查是否是HTTPS URL
    bool isHttps = strncmp(url, "https://", 8) == 0;
    Serial.printf("HTTP GET请求: %s协议\n", isHttps ? "HTTPS" : "HTTP");

    // 1. 激活PDP上下文
    // String cmd = "AT+SAPBR=3,1,\"CONTYPE\",\"GPRS\"";
    // String response = sendATCommand(cmd.c_str(), 1000);
    // if (response.indexOf("OK") == -1) {
    //     Serial.println("设置PDP上下文类型失败");
    //     return "";
    // }

    // 2. 设置APN参数（空字符串表示使用自动获取的APN）
    // cmd = "AT+SAPBR=3,1,\"APN\",\"\"";
    // response = sendATCommand(cmd.c_str(), 1000);
    // if (response.indexOf("OK") == -1) {
    //     Serial.println("设置APN参数失败");
    //     return "";
    // }

    // 3. 激活PDP上下文
    // cmd = "AT+SAPBR=1,1";
    // response = sendATCommand(cmd.c_str(), 5000);
    // if (response.indexOf("OK") == -1) {
    //     // 如果激活失败，可能是已经激活，尝试查询状态
    //     response = sendATCommand("AT+SAPBR=2,1", 1000);
    //     if (response.indexOf("+SAPBR: 1,1") == -1) {
    //         Serial.println("激活PDP上下文失败");
    //         Serial.println("响应: " + response);
    //         return "";
    //     } else {
    //         Serial.println("PDP上下文已经激活");
    //     }
    // } else {
    //     Serial.println("PDP上下文激活成功");
    // }

    // 4. 初始化HTTP服务
    String cmd = "AT+HTTPINIT";
    String response = sendATCommand(cmd.c_str(), 1000);
    if (response.indexOf("OK") == -1) {
        Serial.println("HTTP初始化失败");
        // 尝试关闭可能已存在的HTTP服务
        sendATCommand("AT+HTTPTERM", 1000);
        // 重新初始化
        response = sendATCommand("AT+HTTPINIT", 1000);
        if (response.indexOf("OK") == -1) {
            Serial.println("HTTP重新初始化失败");
            return "";
        }
    }

    // 5. 设置HTTP会话参数: CID
    cmd = "AT+HTTPPARA=\"CID\",1";
    response = sendATCommand(cmd.c_str(), 1000);
    if (response.indexOf("OK") == -1) {
        Serial.println("HTTP设置CID失败");
        sendATCommand("AT+HTTPTERM", 1000);
        return "";
    }

    // 6. 如果是HTTPS，配置SSL
    if (isHttps) {
        cmd = "AT+HTTPSSL=1";
        response = sendATCommand(cmd.c_str(), 1000);
        if (response.indexOf("OK") == -1) {
            Serial.println("HTTPS SSL开启失败");
            sendATCommand("AT+HTTPTERM", 1000);
            return "";
        }
    }

    // 7. 设置URL
    cmd = "AT+HTTPPARA=\"URL\",\"";
    cmd += url;
    cmd += "\"";
    response = sendATCommand(cmd.c_str(), 1000);
    if (response.indexOf("OK") == -1) {
        Serial.println("HTTP设置URL失败");
        sendATCommand("AT+HTTPTERM", 1000);
        return "";
    }

    // 8. 发送GET请求
    cmd = "AT+HTTPACTION=0"; // 0表示GET请求
    response = sendATCommand(cmd.c_str(), timeout);

    // 等待+HTTPACTION响应
    unsigned long startTime = millis();
    bool actionReceived = false;
    int statusCode = 0;
    int dataLen = 0;

    // 检查是否已经在响应中包含了+HTTPACTION
    int actionPos = response.indexOf("+HTTPACTION: 0,");
    if (actionPos != -1) {
        actionReceived = true;

        // 提取+HTTPACTION行
        int lineStart = actionPos;
        int lineEnd = response.indexOf("\r\n", lineStart);
        if (lineEnd == -1) {
            lineEnd = response.length();
        }

        String actionLine = response.substring(lineStart, lineEnd);
        actionLine.trim();
        Serial.println("解析HTTP响应: " + actionLine);

        // 分割字符串获取状态码和数据长度
        int firstComma = actionLine.indexOf(",");
        if (firstComma != -1) {
            int secondComma = actionLine.indexOf(",", firstComma + 1);
            if (secondComma != -1) {
                // 提取状态码
                String statusCodeStr = actionLine.substring(firstComma + 1, secondComma);
                statusCode = statusCodeStr.toInt();

                // 提取数据长度
                String dataLenStr = actionLine.substring(secondComma + 1);
                dataLen = dataLenStr.toInt();

                Serial.printf("解析结果: 状态码=%d, 数据长度=%d\n", statusCode, dataLen);
            }
        }
    }

    // 如果响应中没有包含+HTTPACTION，则等待
    while (!actionReceived && (millis() - startTime < timeout)) {
        if (AIR780E_SERIAL.available()) {
            String line = AIR780E_SERIAL.readStringUntil('\n');
            Serial.println("收到HTTP响应: " + line);

            if (line.indexOf("+HTTPACTION: 0,") != -1) {
                actionReceived = true;

                // 解析状态码和数据长度
                // 格式: +HTTPACTION: 0,200,75
                String actionLine = line;
                actionLine.trim();
                Serial.println("解析HTTP响应: " + actionLine);

                // 分割字符串获取状态码和数据长度
                int firstComma = actionLine.indexOf(",");
                if (firstComma != -1) {
                    int secondComma = actionLine.indexOf(",", firstComma + 1);
                    if (secondComma != -1) {
                        // 提取状态码
                        String statusCodeStr = actionLine.substring(firstComma + 1, secondComma);
                        statusCode = statusCodeStr.toInt();

                        // 提取数据长度
                        String dataLenStr = actionLine.substring(secondComma + 1);
                        dataLen = dataLenStr.toInt();

                        Serial.printf("解析结果: 状态码=%d, 数据长度=%d\n", statusCode, dataLen);
                    }
                }

                break;
            }
        }
        delay(100);
    }

    if (!actionReceived) {
        Serial.println("HTTP GET请求超时，未收到+HTTPACTION响应");
        sendATCommand("AT+HTTPTERM", 1000);
        return "";
    }

    if (statusCode != 200 && statusCode != 206) {
        Serial.printf("HTTP GET请求失败，状态码: %d\n", statusCode);
        sendATCommand("AT+HTTPTERM", 1000);
        return "";
    }

    // 9. 读取响应内容
    String data = "";
    if (dataLen > 0) {
        cmd = "AT+HTTPREAD";
        response = sendATCommand(cmd.c_str(), 5000);

        // 解析响应内容
        int readPos = response.indexOf("+HTTPREAD: ");
        if (readPos != -1) {
            int dataLenStart = readPos + 11; // "+HTTPREAD: " 的长度
            int dataLenEnd = response.indexOf("\r\n", dataLenStart);
            int actualDataLen = response.substring(dataLenStart, dataLenEnd).toInt();

            if (actualDataLen > 0) {
                // 提取响应内容
                int dataStart = dataLenEnd + 2; // 跳过"\r\n"
                int dataEnd = response.indexOf("\r\nOK", dataStart);
                if (dataEnd == -1) {
                    dataEnd = response.length();
                }
                data = response.substring(dataStart, dataEnd);
            } else {
                Serial.println("HTTP GET响应内容为空");
            }
        } else {
            Serial.println("HTTP GET读取响应失败，未找到+HTTPREAD标记");
        }
    } else {
        Serial.println("HTTP GET响应内容长度为0");
    }

    // 10. 终止HTTP服务
    sendATCommand("AT+HTTPTERM", 1000);

    return data;
}

// 配置NTP服务器
bool Air780E::configNTP(const char* server, int timezone) {
    Serial.println("开始配置NTP服务器...");

    // 1. 设置PDP上下文类型为GPRS
    // String response = sendATCommand("AT+SAPBR=3,1,\"Contype\",\"GPRS\"", 1000);
    // if (response.indexOf("OK") == -1) {
    //     Serial.println("设置PDP上下文类型失败");
    //     return false;
    // }

    // 2. 设置APN参数（空字符串表示使用自动获取的APN）
    // response = sendATCommand("AT+SAPBR=3,1,\"APN\",\"\"", 1000);
    // if (response.indexOf("OK") == -1) {
    //     Serial.println("设置APN参数失败");
    //     return false;
    // }

    // 3. 激活PDP上下文
    String response = sendATCommand("AT+SAPBR=1,1", 5000); // 增加超时时间，激活PDP可能需要较长时间
    if (response.indexOf("OK") == -1) {
        // 如果激活失败，可能是已经激活，尝试查询状态
        response = sendATCommand("AT+SAPBR=2,1", 1000);
        if (response.indexOf("+SAPBR: 1,1") == -1) {
            Serial.println("激活PDP上下文失败");
            Serial.println("响应: " + response);
            return false;
        } else {
            Serial.println("PDP上下文已经激活");
        }
    } else {
        Serial.println("PDP上下文激活成功");
    }

    // 4. 设置使用的PDP上下文ID
    response = sendATCommand("AT+CNTPCID=1", 1000);
    if (response.indexOf("OK") == -1) {
        Serial.println("设置NTP使用的PDP上下文ID失败");
        return false;
    }

    // 5. 设置NTP服务器和时区
    String cmd = "AT+CNTP=\"";
    cmd += server;
    cmd += "\",";
    cmd += timezone;
    response = sendATCommand(cmd.c_str(), 1000);

    if (response.indexOf("OK") == -1) {
        Serial.println("NTP服务器配置失败");
        return false;
    }

    Serial.printf("NTP服务器配置成功: %s, 时区: %d\n", server, timezone);
    return true;
}

// 同步网络时间
bool Air780E::syncNTP() {
    Serial.println("开始同步网络时间...");

    // 同步网络时间
    String response = sendATCommand("AT+CNTP", 10000); // 增加超时时间，NTP同步可能需要较长时间

    // 检查是否同步成功
    if (response.indexOf("+CNTP: 1") != -1) {
        Serial.println("NTP时间同步成功");

        // 等待一下，确保时间已经更新
        delay(500);

        // 查询当前时间，确认同步成功
        response = sendATCommand("AT+CCLK?", 1000);
        Serial.println("当前时间: " + response);

        return true;
    } else {
        Serial.println("NTP时间同步失败");
        Serial.println("响应: " + response);
        return false;
    }
}

// 获取当前时间
bool Air780E::getTime(int* year, int* month, int* day, int* hour, int* minute, int* second) {
    // 获取当前时间
    String response = sendATCommand("AT+CCLK?", 1000);

    // 解析时间
    // +CCLK: "yy/MM/dd,hh:mm:ss+zz"
    int cclkPos = response.indexOf("+CCLK: \"");
    if (cclkPos == -1) {
        Serial.println("获取时间失败");
        return false;
    }

    // 提取时间字符串
    String timeStr = response.substring(cclkPos + 8, response.indexOf("\"", cclkPos + 8));
    Serial.println("时间字符串: " + timeStr);

    // 解析时间
    // 格式: yy/MM/dd,hh:mm:ss+zz
    int y, m, d, h, min, s;
    char tz[4];
    if (sscanf(timeStr.c_str(), "%d/%d/%d,%d:%d:%d%3s", &y, &m, &d, &h, &min, &s, tz) >= 6) {
        // 转换年份（两位数年份转为四位数）
        y += 2000;

        // 设置输出参数
        if (year) *year = y;
        if (month) *month = m;
        if (day) *day = d;
        if (hour) *hour = h;
        if (minute) *minute = min;
        if (second) *second = s;

        Serial.printf("当前时间: %04d-%02d-%02d %02d:%02d:%02d\n", y, m, d, h, min, s);
        return true;
    }

    Serial.println("解析时间失败");
    return false;
}

// 获取当前Unix时间戳（秒）
time_t Air780E::getUnixTime() {
    int year, month, day, hour, minute, second;

    if (!getTime(&year, &month, &day, &hour, &minute, &second)) {
        return 0; // 获取时间失败
    }

    // 构建tm结构
    struct tm timeinfo;
    timeinfo.tm_year = year - 1900; // 年份从1900年开始
    timeinfo.tm_mon = month - 1;    // 月份从0开始
    timeinfo.tm_mday = day;
    timeinfo.tm_hour = hour;
    timeinfo.tm_min = minute;
    timeinfo.tm_sec = second;

    // 转换为Unix时间戳
    time_t timestamp = mktime(&timeinfo);
    Serial.printf("Unix时间戳: %ld\n", (long)timestamp);

    return timestamp;
}

// 发送HTTP POST请求
String Air780E::httpPost(const char* url, const char* contentType, const char* data, unsigned long timeout) {
    if (!isConnected()) {
        Serial.println("HTTP POST失败: 网络未连接");
        return "";
    }

    // 检查是否是HTTPS URL
    bool isHttps = strncmp(url, "https://", 8) == 0;
    Serial.printf("HTTP POST请求: %s协议\n", isHttps ? "HTTPS" : "HTTP");

    // 1. 激活PDP上下文
    // String cmd = "AT+SAPBR=3,1,\"CONTYPE\",\"GPRS\"";
    // String response = sendATCommand(cmd.c_str(), 1000);
    // if (response.indexOf("OK") == -1) {
    //     Serial.println("设置PDP上下文类型失败");
    //     return "";
    // }

    // 2. 设置APN参数（空字符串表示使用自动获取的APN）
    // cmd = "AT+SAPBR=3,1,\"APN\",\"\"";
    // response = sendATCommand(cmd.c_str(), 1000);
    // if (response.indexOf("OK") == -1) {
    //     Serial.println("设置APN参数失败");
    //     return "";
    // }

    // 3. 激活PDP上下文
    // cmd = "AT+SAPBR=1,1";
    // response = sendATCommand(cmd.c_str(), 5000);
    // if (response.indexOf("OK") == -1) {
    //     // 如果激活失败，可能是已经激活，尝试查询状态
    //     response = sendATCommand("AT+SAPBR=2,1", 1000);
    //     if (response.indexOf("+SAPBR: 1,1") == -1) {
    //         Serial.println("激活PDP上下文失败");
    //         Serial.println("响应: " + response);
    //         return "";
    //     } else {
    //         Serial.println("PDP上下文已经激活");
    //     }
    // } else {
    //     Serial.println("PDP上下文激活成功");
    // }

    // 4. 初始化HTTP服务
    String cmd = "AT+HTTPINIT";
    String response = sendATCommand(cmd.c_str(), 1000);
    if (response.indexOf("OK") == -1) {
        Serial.println("HTTP初始化失败");
        // 尝试关闭可能已存在的HTTP服务
        sendATCommand("AT+HTTPTERM", 1000);
        // 重新初始化
        response = sendATCommand("AT+HTTPINIT", 1000);
        if (response.indexOf("OK") == -1) {
            Serial.println("HTTP重新初始化失败");
            return "";
        }
    }

    // 5. 设置HTTP会话参数: CID
    cmd = "AT+HTTPPARA=\"CID\",1";
    response = sendATCommand(cmd.c_str(), 1000);
    if (response.indexOf("OK") == -1) {
        Serial.println("HTTP设置CID失败");
        sendATCommand("AT+HTTPTERM", 1000);
        return "";
    }

    // 6. 如果是HTTPS，配置SSL
    if (isHttps) {
        cmd = "AT+HTTPSSL=1";
        response = sendATCommand(cmd.c_str(), 1000);
        if (response.indexOf("OK") == -1) {
            Serial.println("HTTPS SSL开启失败");
            sendATCommand("AT+HTTPTERM", 1000);
            return "";
        }
    }

    // 7. 设置URL
    cmd = "AT+HTTPPARA=\"URL\",\"";
    cmd += url;
    cmd += "\"";
    response = sendATCommand(cmd.c_str(), 1000);
    if (response.indexOf("OK") == -1) {
        Serial.println("HTTP设置URL失败");
        sendATCommand("AT+HTTPTERM", 1000);
        return "";
    }

    // 8. 设置Content-Type
    cmd = "AT+HTTPPARA=\"CONTENT\",\"";
    cmd += contentType;
    cmd += "\"";
    response = sendATCommand(cmd.c_str(), 1000);
    if (response.indexOf("OK") == -1) {
        Serial.println("HTTP设置Content-Type失败");
        sendATCommand("AT+HTTPTERM", 1000);
        return "";
    }

    // 9. 设置数据长度并准备发送数据
    int dataLength = strlen(data);
    cmd = "AT+HTTPDATA=";
    cmd += dataLength;
    cmd += ",";
    cmd += 10000; // 10秒超时
    response = sendATCommand(cmd.c_str(), 1000);
    if (response.indexOf("DOWNLOAD") == -1) {
        Serial.println("HTTP准备数据失败");
        sendATCommand("AT+HTTPTERM", 1000);
        return "";
    }

    // 10. 发送数据
    response = sendATCommand(data, 10000);
    if (response.indexOf("OK") == -1) {
        Serial.println("HTTP发送数据失败");
        sendATCommand("AT+HTTPTERM", 1000);
        return "";
    }

    // 11. 发送POST请求
    cmd = "AT+HTTPACTION=1"; // 1表示POST请求
    response = sendATCommand(cmd.c_str(), timeout);

    // 等待+HTTPACTION响应
    unsigned long startTime = millis();
    bool actionReceived = false;
    int statusCode = 0;
    int dataLen = 0;

    // 检查是否已经在响应中包含了+HTTPACTION
    int actionPos = response.indexOf("+HTTPACTION: 1,");
    if (actionPos != -1) {
        actionReceived = true;

        // 提取+HTTPACTION行
        int lineStart = actionPos;
        int lineEnd = response.indexOf("\r\n", lineStart);
        if (lineEnd == -1) {
            lineEnd = response.length();
        }

        String actionLine = response.substring(lineStart, lineEnd);
        actionLine.trim();
        Serial.println("解析HTTP响应: " + actionLine);

        // 分割字符串获取状态码和数据长度
        int firstComma = actionLine.indexOf(",");
        if (firstComma != -1) {
            int secondComma = actionLine.indexOf(",", firstComma + 1);
            if (secondComma != -1) {
                // 提取状态码
                String statusCodeStr = actionLine.substring(firstComma + 1, secondComma);
                statusCode = statusCodeStr.toInt();

                // 提取数据长度
                String dataLenStr = actionLine.substring(secondComma + 1);
                dataLen = dataLenStr.toInt();

                Serial.printf("解析结果: 状态码=%d, 数据长度=%d\n", statusCode, dataLen);
            }
        }
    }

    // 如果响应中没有包含+HTTPACTION，则等待
    while (!actionReceived && (millis() - startTime < timeout)) {
        if (AIR780E_SERIAL.available()) {
            String line = AIR780E_SERIAL.readStringUntil('\n');
            Serial.println("收到HTTP响应: " + line);

            if (line.indexOf("+HTTPACTION: 1,") != -1) {
                actionReceived = true;

                // 解析状态码和数据长度
                // 格式: +HTTPACTION: 1,200,75
                String actionLine = line;
                actionLine.trim();
                Serial.println("解析HTTP响应: " + actionLine);

                // 分割字符串获取状态码和数据长度
                int firstComma = actionLine.indexOf(",");
                if (firstComma != -1) {
                    int secondComma = actionLine.indexOf(",", firstComma + 1);
                    if (secondComma != -1) {
                        // 提取状态码
                        String statusCodeStr = actionLine.substring(firstComma + 1, secondComma);
                        statusCode = statusCodeStr.toInt();

                        // 提取数据长度
                        String dataLenStr = actionLine.substring(secondComma + 1);
                        dataLen = dataLenStr.toInt();

                        Serial.printf("解析结果: 状态码=%d, 数据长度=%d\n", statusCode, dataLen);
                    }
                }

                break;
            }
        }
        delay(100);
    }

    if (!actionReceived) {
        Serial.println("HTTP POST请求超时，未收到+HTTPACTION响应");
        sendATCommand("AT+HTTPTERM", 1000);
        return "";
    }

    if (statusCode != 200 && statusCode != 201 && statusCode != 202 && statusCode != 204) {
        Serial.printf("HTTP POST请求失败，状态码: %d\n", statusCode);
        sendATCommand("AT+HTTPTERM", 1000);
        return "";
    }

    // 12. 读取响应内容
    String responseData = "";
    if (dataLen > 0) {
        cmd = "AT+HTTPREAD";
        response = sendATCommand(cmd.c_str(), 5000);

        // 解析响应内容
        int readPos = response.indexOf("+HTTPREAD: ");
        if (readPos != -1) {
            int dataLenStart = readPos + 11; // "+HTTPREAD: " 的长度
            int dataLenEnd = response.indexOf("\r\n", dataLenStart);
            int actualDataLen = response.substring(dataLenStart, dataLenEnd).toInt();

            if (actualDataLen > 0) {
                // 提取响应内容
                int dataStart = dataLenEnd + 2; // 跳过"\r\n"
                int dataEnd = response.indexOf("\r\nOK", dataStart);
                if (dataEnd == -1) {
                    dataEnd = response.length();
                }
                responseData = response.substring(dataStart, dataEnd);
            } else {
                Serial.println("HTTP POST响应内容为空");
            }
        } else {
            Serial.println("HTTP POST读取响应失败，未找到+HTTPREAD标记");
        }
    } else {
        Serial.println("HTTP POST响应内容长度为0");
    }

    // 13. 终止HTTP服务
    sendATCommand("AT+HTTPTERM", 1000);

    return responseData;
}
