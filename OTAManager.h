#ifndef OTA_MANAGER_H
#define OTA_MANAGER_H

#include <Arduino.h>
#include <Update.h>
#include <WiFi.h>
#include <ESPmDNS.h>
#include <ArduinoOTA.h>
#include "config.h"

class OTAManager {
public:
    OTAManager();
    
    // 初始化OTA管理器
    bool begin();
    
    // 启动OTA服务
    bool start();
    
    // 停止OTA服务
    bool stop();
    
    // 处理OTA事件
    void handleEvents();
    
    // 设置OTA密码
    void setPassword(const char* password);
    
    // 设置主机名
    void setHostname(const char* hostname);
    
    // 检查是否正在进行OTA升级
    bool isUpdating();

private:
    bool _isEnabled;
    bool _isUpdating;
    char _hostname[32];
    char _password[64];
};

#endif // OTA_MANAGER_H
