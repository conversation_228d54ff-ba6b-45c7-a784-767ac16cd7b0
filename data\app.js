// 全局变量
let config = {};
let status = {};
let statusUpdateInterval;

// DOM加载完成后执行
document.addEventListener('DOMContentLoaded', () => {
    try {
        // 初始化页面导航
        initNavigation();

        // 加载配置和状态
        setTimeout(() => {
            try {
                loadConfig();
                loadStatus();
            } catch (err) {
                console.error('加载配置或状态时出错:', err);
                showToast('加载配置或状态时出错: ' + err.message);
            }
        }, 500); // 延迟500毫秒，确保DOM已完全加载

        // 设置状态自动更新(每5秒)
        statusUpdateInterval = setInterval(() => {
            try {
                loadStatus();
            } catch (err) {
                console.error('自动更新状态时出错:', err);
            }
        }, 5000);

        // 绑定事件处理程序
        setTimeout(() => {
            try {
                bindEvents();
            } catch (err) {
                console.error('绑定事件处理程序时出错:', err);
                showToast('绑定事件处理程序时出错: ' + err.message);
            }
        }, 1000); // 延迟1秒，确保DOM已完全加载
    } catch (err) {
        console.error('初始化页面时出错:', err);
        alert('初始化页面时出错: ' + err.message);
    }
});

// 初始化页面导航
function initNavigation() {
    const navLinks = document.querySelectorAll('nav a');
    const pages = document.querySelectorAll('.page');

    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();

            // 移除所有活动类
            navLinks.forEach(l => l.classList.remove('active'));
            pages.forEach(p => p.classList.remove('active'));

            // 添加活动类到当前链接和页面
            link.classList.add('active');
            const pageId = link.getAttribute('data-page');
            document.getElementById(pageId).classList.add('active');
        });
    });
}

// 加载配置
function loadConfig() {
    console.log('开始加载配置...');
    fetch('/api/config')
        .then(response => {
            console.log('配置响应状态:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('接收到配置数据:', data);
            config = data;

            // 检查WiFi和4G配置
            console.log('WiFi配置:', config.wifi);
            console.log('4G配置:', config.cellular);

            try {
                updateConfigUI();
                console.log('配置UI更新成功');
            } catch (err) {
                console.error('更新配置UI时出错:', err);
                showToast('更新配置UI时出错: ' + err.message);
            }
        })
        .catch(error => {
            console.error('加载配置失败:', error);
            showToast('加载配置失败: ' + error.message);
        });
}

// 加载状态
function loadStatus() {
    fetch('/api/status')
        .then(response => response.json())
        .then(data => {
            status = data;
            try {
                updateStatusUI();
            } catch (err) {
                console.error('更新状态UI时出错:', err);
                showToast('更新状态UI时出错: ' + err.message);
            }
        })
        .catch(error => {
            console.error('加载状态失败:', error);
            showToast('加载状态失败: ' + error.message);
        });
}

// 更新配置UI
function updateConfigUI() {
    try {
        // 设备信息
        const deviceNameElement = document.getElementById('device-name');
        if (deviceNameElement) {
            deviceNameElement.value = config.device_name || '';
        }

        // 网络模式
        const networkModeElement = document.getElementById('network-mode-select');
        if (networkModeElement) {
            networkModeElement.value = config.network_mode;
        }

        // WiFi设置
        const wifiEnabledElement = document.getElementById('wifi-enabled');
        if (wifiEnabledElement) {
            // 确保wifi.enabled有值，默认为true
            wifiEnabledElement.checked = config.wifi?.enabled !== false;
            console.log('WiFi启用状态:', wifiEnabledElement.checked);
        }

        const wifiSsidElement = document.getElementById('wifi-ssid');
        if (wifiSsidElement) {
            wifiSsidElement.value = config.wifi?.ssid || '';
            console.log('WiFi SSID:', wifiSsidElement.value);
        }

        const wifiPasswordElement = document.getElementById('wifi-password');
        if (wifiPasswordElement) {
            wifiPasswordElement.value = config.wifi?.password || ''; // 显示真实密码
            console.log('WiFi密码长度:', wifiPasswordElement.value.length);
        }

        // 4G设置
        const cellularEnabledElement = document.getElementById('cellular-enabled');
        if (cellularEnabledElement) {
            // 确保cellular.enabled有值，默认为true
            cellularEnabledElement.checked = config.cellular?.enabled !== false;
            console.log('4G启用状态:', cellularEnabledElement.checked);
        }

        const cellularApnElement = document.getElementById('cellular-apn');
        if (cellularApnElement) {
            cellularApnElement.value = config.cellular?.apn || '';
        }

        const cellularUserElement = document.getElementById('cellular-user');
        if (cellularUserElement) {
            cellularUserElement.value = config.cellular?.user || '';
        }

        const cellularPasswordElement = document.getElementById('cellular-password');
        if (cellularPasswordElement) {
            cellularPasswordElement.value = config.cellular?.password || ''; // 显示真实密码
        }

        // MQTT设置
        const mqttServerElement = document.getElementById('mqtt-server-input');
        if (mqttServerElement) {
            mqttServerElement.value = config.mqtt?.server || '';
        }

        const mqttPortElement = document.getElementById('mqtt-port');
        if (mqttPortElement) {
            mqttPortElement.value = config.mqtt?.port || 1883;
        }

        const mqttUserElement = document.getElementById('mqtt-user');
        if (mqttUserElement) {
            mqttUserElement.value = config.mqtt?.user || '';
        }

        const mqttPasswordElement = document.getElementById('mqtt-password');
        if (mqttPasswordElement) {
            mqttPasswordElement.value = config.mqtt?.password || ''; // 显示真实密码
        }

        // 显示MAC地址作为客户端ID（只读）
        // 优先使用配置中的客户端ID，因为它已经被设置为MAC地址
        const mqttClientIdElement = document.getElementById('mqtt-client-id');
        if (mqttClientIdElement) {
            mqttClientIdElement.value = config.mqtt?.client_id || '';
        }

        const mqttTopicStatusElement = document.getElementById('mqtt-topic-status');
        if (mqttTopicStatusElement) {
            mqttTopicStatusElement.value = config.mqtt?.topic_status || '';
        }

        const mqttTopicCommandElement = document.getElementById('mqtt-topic-command');
        if (mqttTopicCommandElement) {
            mqttTopicCommandElement.value = config.mqtt?.topic_command || '';
        }

        const mqttTopicLocationElement = document.getElementById('mqtt-topic-location');
        if (mqttTopicLocationElement) {
            mqttTopicLocationElement.value = config.mqtt?.topic_location || '';
        }

        const mqttUpdateIntervalElement = document.getElementById('mqtt-update-interval');
        if (mqttUpdateIntervalElement) {
            mqttUpdateIntervalElement.value = config.mqtt?.update_interval || 60;
        }

        // API接口配置
        const apiGoodsOutElement = document.getElementById('api-goods-out');
        if (apiGoodsOutElement) {
            apiGoodsOutElement.value = config.api?.goodsOut || '';
            console.log('API出货接口:', apiGoodsOutElement.value);
        }

        const apiCabinetBinTestElement = document.getElementById('api-cabinet-bin-test');
        if (apiCabinetBinTestElement) {
            apiCabinetBinTestElement.value = config.api?.cabinetBinTest || '';
            console.log('API货道测试接口:', apiCabinetBinTestElement.value);
        }

        // const apiGetDeviceParamsElement = document.getElementById('api-get-device-params');
        // if (apiGetDeviceParamsElement) {
        //     apiGetDeviceParamsElement.value = config.api?.getDeviceParams || '';
        //     console.log('API设备参数接口:', apiGetDeviceParamsElement.value);
        // }
    } catch (err) {
        console.error('更新配置UI时出错:', err);
        throw err; // 重新抛出错误，让调用者处理
    }
}

// 更新状态UI
function updateStatusUI() {
    try {
        // 顶部状态栏
        const connectionStatusElement = document.getElementById('connection-status');
        if (connectionStatusElement) {
            connectionStatusElement.textContent = `连接状态: ${status.mqtt_connected ? '已连接' : '未连接'}`;
        }

        const networkModeElement = document.getElementById('network-mode');
        if (networkModeElement) {
            networkModeElement.textContent = `网络模式: ${status.network_mode || '未知'}`;
        }

        const signalStrengthElement = document.getElementById('signal-strength');
        if (signalStrengthElement) {
            signalStrengthElement.textContent = `信号强度: ${status.signal_strength || 0}%`;
        }

        // 网络状态
        const wifiStatusElement = document.getElementById('wifi-status');
        if (wifiStatusElement) {
            wifiStatusElement.textContent = status.wifi_connected ? '已连接' : '未连接';
        }

        const cellularStatusElement = document.getElementById('cellular-status');
        if (cellularStatusElement) {
            cellularStatusElement.textContent = status.cellular_connected ? '已连接' : '未连接';
        }

        const ipAddressElement = document.getElementById('ip-address');
        if (ipAddressElement) {
            ipAddressElement.textContent = status.ip_address || '未知';
        }

        // MQTT状态
        const mqttStatusElement = document.getElementById('mqtt-status');
        if (mqttStatusElement) {
            mqttStatusElement.textContent = status.mqtt_connected ? '已连接' : '未连接';
        }

        const mqttServerElement = document.getElementById('mqtt-server');
        if (mqttServerElement) {
            mqttServerElement.textContent = config.mqtt?.server || '未知';
        }

        // 设备状态
        const motorStatusElement = document.getElementById('motor-status');
        if (motorStatusElement) {
            motorStatusElement.textContent = status.motor_running ? '运行中' : '停止';
        }

        const irStatusElement = document.getElementById('ir-status');
        if (irStatusElement) {
            irStatusElement.textContent = status.ir_triggered ? '已触发' : '未触发';
        }

        // 系统信息
        const firmwareVersionElement = document.getElementById('firmware-version');
        if (firmwareVersionElement) {
            firmwareVersionElement.textContent = status.firmware || '未知';
        }

        // 固件升级页面的固件版本信息
        const currentFirmwareVersionElement = document.getElementById('current-firmware-version');
        if (currentFirmwareVersionElement) {
            currentFirmwareVersionElement.textContent = status.firmware || '未知';
        }

        const uptimeElement = document.getElementById('uptime');
        if (uptimeElement) {
            uptimeElement.textContent = formatTime(status.uptime || 0);
        }

        const freeHeapElement = document.getElementById('free-heap');
        if (freeHeapElement) {
            freeHeapElement.textContent = formatBytes(status.free_heap || 0);
        }

        // 固件升级页面的可用空间信息
        const availableSpaceElement = document.getElementById('available-space');
        if (availableSpaceElement) {
            availableSpaceElement.textContent = formatBytes(status.sketch_space || 0);
        }

        const macAddressElement = document.getElementById('mac-address');
        if (macAddressElement) {
            macAddressElement.textContent = status.mac_address || '未知';
        }

        // 位置信息
        const latitudeElement = document.getElementById('latitude');
        if (latitudeElement) {
            latitudeElement.textContent = status.latitude || '未知';
        }

        const longitudeElement = document.getElementById('longitude');
        if (longitudeElement) {
            longitudeElement.textContent = status.longitude || '未知';
        }

        const locationUpdateElement = document.getElementById('location-update');
        if (locationUpdateElement) {
            locationUpdateElement.textContent = status.location_update ? formatTime((Date.now() / 1000 - status.location_update) | 0) + '前' : '未知';
        }
    } catch (err) {
        console.error('更新状态UI基本信息时出错:', err);
        // 继续执行，尝试更新其他部分
    }

    // 更新红外传感器状态
    if (document.getElementById('ir-sensor1-status')) {
        document.getElementById('ir-sensor1-status').textContent = status.ir_triggered ? '已触发' : '未触发';
    }
    if (document.getElementById('ir-sensor2-status')) {
        document.getElementById('ir-sensor2-status').textContent = status.ir_triggered2 ? '已触发' : '未触发';
    }
    if (document.getElementById('ir-sensor3-status')) {
        document.getElementById('ir-sensor3-status').textContent = status.ir_triggered3 ? '已触发' : '未触发';
    }
    if (document.getElementById('pir-sensor-status')) {
        document.getElementById('pir-sensor-status').textContent = status.pir_triggered ? '已触发' : '未触发';
    }

    // 更新继电器状态
    if (status.relay_status) {
        for (let i = 0; i < status.relay_status.length; i++) {
            const relayElement = document.getElementById(`relay${i+1}-status`);
            if (relayElement) {
                relayElement.textContent = status.relay_status[i] ? '开启' : '关闭';
                relayElement.className = status.relay_status[i] ? 'status-on' : 'status-off';
            }
        }
    }

    // 更新设备通信状态
    if (status.device_communication_error !== undefined) {
        const deviceStatusElement = document.getElementById('device-communication-status');
        if (deviceStatusElement) {
            deviceStatusElement.textContent = status.device_communication_error ? '异常' : '正常';
            deviceStatusElement.className = status.device_communication_error ? 'status-error' : 'status-ok';
        }
    }
}

// 绑定事件处理程序
function bindEvents() {
    // 刷新状态按钮
    document.getElementById('refresh-status').addEventListener('click', loadStatus);

    // 网络设置表单
    document.getElementById('network-form').addEventListener('submit', (e) => {
        e.preventDefault();
        saveNetworkConfig();
    });

    // MQTT设置表单
    document.getElementById('mqtt-form').addEventListener('submit', (e) => {
        e.preventDefault();
        saveMQTTConfig();
    });

    // 设备信息表单
    document.getElementById('device-form').addEventListener('submit', (e) => {
        e.preventDefault();
        saveDeviceConfig();
    });

    // API接口配置表单
    document.getElementById('api-form').addEventListener('submit', (e) => {
        e.preventDefault();
        saveAPIConfig();
    });

    // 控制电机按钮
    if (document.getElementById('control-motor')) {
        document.getElementById('control-motor').addEventListener('click', controlMotor);
    }

    // 出货按钮
    document.getElementById('dispense-product').addEventListener('click', dispenseProduct);

    // 重启系统按钮
    document.getElementById('restart-system').addEventListener('click', restartSystem);

    // OTA升级表单
    if (document.getElementById('ota-form')) {
        document.getElementById('ota-form').addEventListener('submit', (e) => {
            e.preventDefault();
            uploadFirmware();
        });
    }

    // 继电器控制按钮
    // 单个继电器控制
    for (let i = 1; i <= 5; i++) {
        const onButton = document.getElementById(`relay${i}-on`);
        const offButton = document.getElementById(`relay${i}-off`);

        if (onButton) {
            onButton.addEventListener('click', () => {
                controlRelay(i, true);
            });
        }

        if (offButton) {
            offButton.addEventListener('click', () => {
                controlRelay(i, false);
            });
        }
    }

    // 全部继电器控制
    const allOnButton = document.getElementById('all-relays-on');
    const allOffButton = document.getElementById('all-relays-off');

    if (allOnButton) {
        allOnButton.addEventListener('click', () => {
            controlAllRelays(true);
        });
    }

    if (allOffButton) {
        allOffButton.addEventListener('click', () => {
            controlAllRelays(false);
        });
    }
}

// 保存网络配置
function saveNetworkConfig() {
    try {
        // 获取网络模式
        const networkModeSelect = document.getElementById('network-mode-select');
        const networkMode = networkModeSelect ? parseInt(networkModeSelect.value) : 0;

        // 构建网络配置对象
        const networkConfig = {
            network_mode: networkMode,
            wifi: {
                // 由于HTML中wifi_enabled被注释掉了，默认设置为true
                enabled: true,
                ssid: document.getElementById('wifi-ssid')?.value || '',
                password: document.getElementById('wifi-password')?.value || ''
            },
            cellular: {
                // 由于HTML中cellular_enabled被注释掉了，默认设置为true
                enabled: true,
                apn: document.getElementById('cellular-apn')?.value || '',
                user: document.getElementById('cellular-user')?.value || '',
                password: document.getElementById('cellular-password')?.value || ''
            }
        };

        // 打印网络配置，便于调试
        console.log('发送网络配置:', networkConfig);

        fetch('/api/config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(networkConfig)
        })
        .then(response => {
            console.log('网络配置响应状态:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('网络配置响应数据:', data);
            if (data.success) {
                showToast('网络设置已保存');
                // 延迟加载配置，确保服务器有时间处理
                setTimeout(() => {
                    loadConfig();
                }, 1000);
            } else {
                showToast('保存网络设置失败');
            }
        })
        .catch(error => {
            console.error('保存网络设置错误:', error);
            showToast('保存网络设置失败: ' + error.message);
        });
    } catch (err) {
        console.error('保存网络配置时出错:', err);
        showToast('保存网络配置时出错: ' + err.message);
    }
}

// 保存MQTT配置
function saveMQTTConfig() {
    try {
        // 构建MQTT配置对象
        const mqttConfig = {
            mqtt: {
                server: document.getElementById('mqtt-server-input')?.value || '',
                port: parseInt(document.getElementById('mqtt-port')?.value || '1883'),
                user: document.getElementById('mqtt-user')?.value || '',
                password: document.getElementById('mqtt-password')?.value || '',
                // 不发送客户端ID，服务器端会自动使用MAC地址
                // client_id: document.getElementById('mqtt-client-id').value,
                topic_status: document.getElementById('mqtt-topic-status')?.value || '',
                topic_command: document.getElementById('mqtt-topic-command')?.value || '',
                topic_location: document.getElementById('mqtt-topic-location')?.value || '',
                update_interval: parseInt(document.getElementById('mqtt-update-interval')?.value || '60')
            }
        };

        // 打印MQTT配置，便于调试
        console.log('发送MQTT配置:', mqttConfig);

        fetch('/api/config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(mqttConfig)
        })
        .then(response => {
            console.log('MQTT配置响应状态:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('MQTT配置响应数据:', data);
            if (data.success) {
                showToast('MQTT设置已保存');
                // 延迟加载配置，确保服务器有时间处理
                setTimeout(() => {
                    loadConfig();
                }, 1000);
            } else {
                showToast('保存MQTT设置失败');
            }
        })
        .catch(error => {
            console.error('保存MQTT设置错误:', error);
            showToast('保存MQTT设置失败: ' + error.message);
        });
    } catch (err) {
        console.error('保存MQTT配置时出错:', err);
        showToast('保存MQTT配置时出错: ' + err.message);
    }
}

// 保存设备配置
function saveDeviceConfig() {
    try {
        // 构建设备配置对象
        const deviceConfig = {
            device_name: document.getElementById('device-name')?.value || 'TXWRGJ'
        };

        // 打印设备配置，便于调试
        console.log('发送设备配置:', deviceConfig);

        fetch('/api/config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(deviceConfig)
        })
        .then(response => {
            console.log('设备配置响应状态:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('设备配置响应数据:', data);
            if (data.success) {
                showToast('设备设置已保存');
                // 延迟加载配置，确保服务器有时间处理
                setTimeout(() => {
                    loadConfig();
                }, 1000);
            } else {
                showToast('保存设备设置失败');
            }
        })
        .catch(error => {
            console.error('保存设备设置错误:', error);
            showToast('保存设备设置失败: ' + error.message);
        });
    } catch (err) {
        console.error('保存设备配置时出错:', err);
        showToast('保存设备配置时出错: ' + err.message);
    }
}

// 保存API接口配置
function saveAPIConfig() {
    try {
        // 构建API接口配置对象
        const apiConfig = {
            api: {
                goodsOut: document.getElementById('api-goods-out')?.value || '',
                cabinetBinTest: document.getElementById('api-cabinet-bin-test')?.value || '',
                // getDeviceParams: document.getElementById('api-get-device-params')?.value || ''
            }
        };

        // 打印API接口配置，便于调试
        console.log('发送API接口配置:', apiConfig);

        fetch('/api/config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(apiConfig)
        })
        .then(response => {
            console.log('API接口配置响应状态:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('API接口配置响应数据:', data);
            if (data.success) {
                showToast('API接口设置已保存');
                // 延迟加载配置，确保服务器有时间处理
                setTimeout(() => {
                    loadConfig();
                }, 1000);
            } else {
                showToast('保存API接口设置失败');
            }
        })
        .catch(error => {
            console.error('保存API接口设置错误:', error);
            showToast('保存API接口设置失败: ' + error.message);
        });
    } catch (err) {
        console.error('保存API接口配置时出错:', err);
        showToast('保存API接口配置时出错: ' + err.message);
    }
}

// 控制电机
function controlMotor() {
    const motorData = {
        address: parseInt(document.getElementById('motor-address').value),
        command: parseInt(document.getElementById('motor-command').value),
        duration: parseInt(document.getElementById('motor-duration').value)
    };

    fetch('/api/motor', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(motorData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('电机控制命令已发送');
            setTimeout(loadStatus, 1000);
        } else {
            showToast('电机控制失败');
        }
    })
    .catch(error => {
        showToast('电机控制失败: ' + error.message);
    });
}

// 出货
function dispenseProduct() {
    const dispenseData = {
        row: parseInt(document.getElementById('dispense-row').value),
        column: parseInt(document.getElementById('dispense-column').value)
    };

    // 更新UI
    document.getElementById('dispense-success').textContent = '处理中...';
    document.getElementById('dispense-ir').textContent = '等待...';

    fetch('/api/dispense', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(dispenseData)
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('dispense-success').textContent = data.success ? '成功' : '失败';
        document.getElementById('dispense-ir').textContent = data.ir_triggered ? '已触发' : '未触发';
        showToast('出货操作完成');
        setTimeout(loadStatus, 1000);
    })
    .catch(error => {
        document.getElementById('dispense-success').textContent = '错误';
        document.getElementById('dispense-ir').textContent = '未知';
        showToast('出货操作失败: ' + error.message);
    });
}

// 重启系统
function restartSystem() {
    if (confirm('确定要重启系统吗？')) {
        fetch('/api/restart', {
            method: 'POST'
        })
        .then(response => {
            showToast('系统正在重启...');

            // 停止状态更新
            clearInterval(statusUpdateInterval);

            // 等待系统重启
            setTimeout(() => {
                // 尝试重新连接
                checkConnection();
            }, 5000);
        })
        .catch(error => {
            showToast('重启系统失败: ' + error.message);
        });
    }
}

// 上传固件
function uploadFirmware() {
    const fileInput = document.getElementById('firmware-file');
    const file = fileInput.files[0];
    const uploadBtn = document.getElementById('upload-firmware-btn');
    const cancelBtn = document.getElementById('cancel-upload-btn');
    const uploadStatus = document.getElementById('upload-status');
    const uploadPercentage = document.getElementById('upload-percentage');
    const progressBar = document.getElementById('upload-progress');

    if (!file) {
        showToast('请选择固件文件');
        return;
    }

    // 检查文件大小和类型
    if (file.size > 3000000) { // 约3MB
        showToast('固件文件过大，请检查是否选择了正确的文件');
        return;
    }

    if (!file.name.toLowerCase().endsWith('.bin')) {
        showToast('请选择.bin格式的固件文件');
        return;
    }

    // 更新UI状态
    uploadBtn.disabled = true;
    cancelBtn.style.display = 'block';
    uploadStatus.textContent = '准备上传...';
    uploadPercentage.textContent = '0%';
    progressBar.style.width = '0%';

    const formData = new FormData();
    formData.append('firmware', file);

    const xhr = new XMLHttpRequest();
    let isUploading = true;

    // 进度处理
    xhr.upload.onprogress = (e) => {
        if (e.lengthComputable && isUploading) {
            const percentComplete = Math.round((e.loaded / e.total) * 100);
            progressBar.style.width = percentComplete + '%';
            uploadPercentage.textContent = percentComplete + '%';

            if (percentComplete < 50) {
                uploadStatus.textContent = '上传中...';
            } else if (percentComplete < 90) {
                uploadStatus.textContent = '继续上传中...';
            } else {
                uploadStatus.textContent = '即将完成...';
            }
        }
    };

    // 完成处理
    xhr.onload = function() {
        isUploading = false;
        if (xhr.status === 200) {
            progressBar.style.width = '100%';
            uploadPercentage.textContent = '100%';
            uploadStatus.textContent = '上传成功，设备正在重启...';
            showToast('固件上传成功，系统将重启');

            // 停止状态更新
            clearInterval(statusUpdateInterval);

            // 禁用取消按钮
            cancelBtn.disabled = true;

            // 等待系统重启
            setTimeout(() => {
                uploadStatus.textContent = '等待设备重启...';
                // 尝试重新连接
                checkConnection();
            }, 5000);
        } else {
            uploadStatus.textContent = '上传失败: ' + (xhr.responseText || xhr.statusText);
            showToast('固件上传失败: ' + (xhr.responseText || xhr.statusText));
            resetUploadUI();
        }
    };

    // 错误处理
    xhr.onerror = function() {
        isUploading = false;
        uploadStatus.textContent = '上传失败: 网络错误';
        showToast('固件上传失败: 网络错误');
        resetUploadUI();
    };

    // 中止处理
    xhr.onabort = function() {
        isUploading = false;
        uploadStatus.textContent = '上传已取消';
        showToast('固件上传已取消');
        resetUploadUI();
    };

    // 发送请求
    xhr.open('POST', '/api/update', true);
    xhr.send(formData);

    // 取消上传
    cancelBtn.onclick = function() {
        if (isUploading) {
            xhr.abort();
            isUploading = false;
        }
    };

    showToast('正在上传固件...');
}

// 重置上传UI
function resetUploadUI() {
    const uploadBtn = document.getElementById('upload-firmware-btn');
    const cancelBtn = document.getElementById('cancel-upload-btn');

    uploadBtn.disabled = false;
    cancelBtn.style.display = 'none';
    document.getElementById('firmware-file').value = '';
}

// 检查连接
function checkConnection() {
    fetch('/api/status')
        .then(() => {
            showToast('已重新连接到设备');

            // 重新加载配置和状态
            loadConfig();
            loadStatus();

            // 重置上传UI
            resetUploadUI();
            document.getElementById('upload-status').textContent = '设备已重启，升级完成';

            // 重新启动状态更新
            statusUpdateInterval = setInterval(loadStatus, 5000);
        })
        .catch(() => {
            showToast('正在等待设备重启...');

            // 继续尝试连接
            setTimeout(checkConnection, 2000);
        });
}

// 显示提示框
function showToast(message) {
    try {
        const toast = document.getElementById('toast');
        if (!toast) {
            console.error('找不到toast元素');
            return;
        }

        toast.textContent = message;
        toast.classList.add('show');

        setTimeout(() => {
            try {
                toast.classList.remove('show');
            } catch (err) {
                console.error('隐藏提示框时出错:', err);
            }
        }, 3000);
    } catch (err) {
        console.error('显示提示框时出错:', err);
        // 如果提示框显示失败，使用alert作为备选
        alert(message);
    }
}

// 格式化时间
function formatTime(seconds) {
    const days = Math.floor(seconds / 86400);
    seconds %= 86400;
    const hours = Math.floor(seconds / 3600);
    seconds %= 3600;
    const minutes = Math.floor(seconds / 60);
    seconds %= 60;

    if (days > 0) {
        return `${days}天 ${hours}小时 ${minutes}分 ${seconds}秒`;
    } else if (hours > 0) {
        return `${hours}小时 ${minutes}分 ${seconds}秒`;
    } else if (minutes > 0) {
        return `${minutes}分 ${seconds}秒`;
    } else {
        return `${seconds}秒`;
    }
}

// 格式化字节数
function formatBytes(bytes) {
    if (bytes < 1024) {
        return bytes + ' B';
    } else if (bytes < (1024 * 1024)) {
        return (bytes / 1024).toFixed(2) + ' KB';
    } else if (bytes < (1024 * 1024 * 1024)) {
        return (bytes / 1024 / 1024).toFixed(2) + ' MB';
    } else {
        return (bytes / 1024 / 1024 / 1024).toFixed(2) + ' GB';
    }
}

// 控制单个继电器
function controlRelay(relayNumber, state) {
    const relayData = {
        relay_number: relayNumber,
        state: state
    };

    fetch('/api/relay', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(relayData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(`继电器${relayNumber}已${state ? '开启' : '关闭'}`);
            setTimeout(loadStatus, 500);
        } else {
            showToast(`继电器${relayNumber}控制失败`);
        }
    })
    .catch(error => {
        showToast(`继电器控制失败: ${error.message}`);
    });
}

// 控制所有继电器
function controlAllRelays(state) {
    const relayData = {
        all_relays: true,
        state: state
    };

    fetch('/api/relay', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(relayData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(`所有继电器已${state ? '开启' : '关闭'}`);
            setTimeout(loadStatus, 500);
        } else {
            showToast('继电器控制失败');
        }
    })
    .catch(error => {
        showToast(`继电器控制失败: ${error.message}`);
    });
}
