#ifndef HARDWARE_NETWORK_MANAGER_H
#define HARDWARE_NETWORK_MANAGER_H

#include <Arduino.h>
#include <WiFi.h>
#include <DNSServer.h>
#include "config.h"
#include "Air780E.h"

class HardwareNetworkManager {
public:
    HardwareNetworkManager(Air780E* air780e);

    // 初始化网络管理器
    bool begin();

    // 配置WiFi
    bool configureWiFi(const char* ssid, const char* password);

    // 配置4G
    bool configure4G(const char* apn, const char* user = "", const char* password = "");

    // 设置网络模式
    bool setNetworkMode(NetworkMode mode);

    // 连接到网络
    bool connect();

    // 断开网络连接
    bool disconnect();

    // 检查网络连接状态
    bool isConnected();

    // 获取当前网络模式
    NetworkMode getNetworkMode();

    // 启动AP模式
    bool startAP(const char* ssid, const char* password);

    // 停止AP模式
    bool stopAP();

    // 检查AP模式是否活跃
    bool isAPActive();

    // 获取当前IP地址
    String getIPAddress();

    // 获取信号强度
    uint8_t getSignalStrength();

    // 获取设备唯一ID（基于ESP32芯片ID）
    String getUniqueDeviceId();

    // 处理网络事件
    void handleEvents();

    // 暂停WiFi连接和恢复WiFi连接功能已移除

private:
    Air780E* _air780e;
    NetworkMode _networkMode;
    bool _isConnected;
    bool _apActive;

    // WiFi配置
    char _wifiSSID[32];
    char _wifiPassword[64];

    // 4G配置
    char _apn[32];
    char _apnUser[32];
    char _apnPassword[32];

    // DNS服务器(用于AP模式)
    DNSServer _dnsServer;

    // 连接WiFi
    bool connectWiFi();

    // 连接4G
    bool connect4G();

    // WiFi连接状态追踪
    bool _wifiConnecting;
    unsigned long _wifiConnectStartTime;
    unsigned long _wifiConnectTimeout;

    // 存储设备唯一ID
    String _deviceId;
};

#endif // HARDWARE_NETWORK_MANAGER_H
