#ifndef WEB_SERVER_MANAGER_H
#define WEB_SERVER_MANAGER_H

#include <Arduino.h>
#include <WebServer.h>
#include <ArduinoJson.h>
#include <LittleFS.h>
#include <Update.h>
#include "config.h"
#include "HardwareNetworkManager.h"
#include "MQTTClient.h"
#include "DeviceControl.h"
#include "OTAManager.h"
#include "Air780E.h"

class WebServerManager {
public:
    WebServerManager(HardwareNetworkManager* networkManager, MQTTClient* mqttClient, DeviceControl* deviceControl, OTAManager* otaManager);

    // 初始化Web服务器
    bool begin();

    // 启动Web服务器
    bool start();

    // 停止Web服务器
    bool stop();

    // 处理Web服务器事件（需要在loop中调用）
    void handleEvents();

    // 更新系统状态
    void updateStatus(const DeviceStatus& status);

    // 应用配置
    bool applyConfig();

    // 检查是否有客户端连接到Web服务器
    bool hasClients();

    // 获取系统配置
    SystemConfig& getConfig() { return _config; }

    // 保存配置
    bool saveConfig();

private:
    HardwareNetworkManager* _hardwareNetworkManager;
    MQTTClient* _mqttClient;
    DeviceControl* _deviceControl;
    OTAManager* _otaManager;
    Air780E* _air780e;
    WebServer _server;
    bool _serverRunning = false; // 跟踪服务器是否在运行

    // 系统配置
    SystemConfig _config;

    // 设备状态
    DeviceStatus _status;

    // 设置路由
    void setupRoutes();

    // API处理函数
    void handleRoot();
    void handleNotFound();
    void handleGetConfig();
    void handleSetConfig();
    void handleGetStatus();
    void handleControlMotor();
    void handleDispenseProduct();
    void handleRelayControl();
    void handleRestart();
    void handleOTAUpload();
    bool handleFileRead(String path);

    // 加载配置
    bool loadConfig();

    // 列出文件系统中的文件
    void listFiles();

    // 获取文件的MIME类型
    String getContentType(String filename);

    // 通过API接口发送出货结果
    bool sendDispenseResultViaAPI(JsonDocument& resultDoc);
};

#endif // WEB_SERVER_MANAGER_H
