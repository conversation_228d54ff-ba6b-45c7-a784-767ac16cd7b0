#ifndef SYSTEM_CORE_H
#define SYSTEM_CORE_H

#include <Arduino.h>
#include <time.h>
#include "config.h"
#include "Air780E.h"
#include "HardwareNetworkManager.h"
#include "MQTTClient.h"
#include "DeviceControl.h"
#include "WebServerManager.h"
#include "OTAManager.h"
#include "ConnectionManager.h"
#include "CommandProcessor.h"
#include "StatusManager.h"
// #include "BluetoothManager.h"

class SystemCore {
public:
    SystemCore();

    // 获取单例实例
    static SystemCore* getInstance();

    // 初始化系统
    bool begin();

    // 系统主循环
    void loop();

    // 获取各个模块的引用
    Air780E& getAir780E() { return _air780e; }
    Air780E* getAir780EPtr() { return &_air780e; }
    HardwareNetworkManager& getNetworkManager() { return _hardwareNetworkManager; }
    MQTTClient& getMqttClient() { return _mqttClient; }
    DeviceControl& getDeviceControl() { return _deviceControl; }
    WebServerManager& getWebServerManager() { return _webServer; }
    OTAManager& getOtaManager() { return _otaManager; }
    ConnectionManager& getConnectionManager() { return _connectionManager; }
    CommandProcessor& getCommandProcessor() { return _commandProcessor; }
    StatusManager& getStatusManager() { return _statusManager; }
    // BluetoothManager& getBluetoothManager() { return _bluetoothManager; }

    // 重启系统
    void restart();

    // 进入AP模式
    void enterAPMode();

    // 同步网络时间
    bool syncNetworkTime();

    // 获取当前Unix时间戳（秒）
    time_t getCurrentUnixTime();

    // 获取当前Unix时间戳（毫秒）
    uint64_t getCurrentUnixTimeMs();

    // 获取当前时间的格式化字符串（年月日时分秒）
    String getCurrentTimeString(const char* format = "%Y-%m-%d %H:%M:%S");

private:
    // 系统组件
    Air780E _air780e;
    HardwareNetworkManager _hardwareNetworkManager;
    MQTTClient _mqttClient;
    DeviceControl _deviceControl;
    OTAManager _otaManager;
    WebServerManager _webServer;
    ConnectionManager _connectionManager;
    CommandProcessor _commandProcessor;
    StatusManager _statusManager;
    // BluetoothManager _bluetoothManager;

    // 处理重置按键
    void handleResetButton();

    // 管理Web服务器状态
    void manageWebServerState();

    // 按键相关变量
    bool _buttonPressed;
    unsigned long _buttonPressStartTime;
    const unsigned long LONG_PRESS_TIME = 10000; // 10秒长按

    // Web服务器状态变量
    bool _webServerRunning;

    // 时间同步相关变量
    bool _timeIsSynced;
    unsigned long _lastTimeSyncAttempt;
    const unsigned long TIME_SYNC_INTERVAL = 3600000; // 1小时同步一次时间
    const unsigned long TIME_SYNC_INITIAL_DELAY = 10000; // 10秒后首次尝试同步

    // 连接状态变化回调
    static void onConnectionStateChange(void* instance, ConnectionState oldState, ConnectionState newState);
};

// 全局系统核心实例
extern SystemCore systemCore;

#endif // SYSTEM_CORE_H
