#ifndef AIR780E_H
#define AIR780E_H

#include <Arduino.h>
#include "config.h"

class Air780E {
public:
    Air780E();

    // 初始化4G模块
    bool begin();

    // 连接到网络
    bool connect(const char* apn, const char* user = "", const char* password = "");

    // 断开网络连接
    bool disconnect();

    // 检查网络连接状态
    bool isConnected();

    // 获取信号强度(0-100)
    uint8_t getSignalStrength();

    // GPS相关方法
    // 开启GNSS电源
    bool gpsOpen();

    // 关闭GNSS电源
    bool gpsClose();

    // 检查GPS是否已开启
    bool isGpsOpen();

    // 获取GPS状态
    int getGpsStatus();

    // 获取位置信息
    bool getLocation(float &latitude, float &longitude);

    // 获取原始GPS信息
    String getGpsInfo();

    // 发送AT命令并获取响应
    String sendATCommand(const char* command, unsigned long timeout = 1000);

    // 重启模块
    bool restart();

    // 获取IMEI号
    String getIMEI();

    // 获取SIM卡ICCID
    String getICCID();

    // 获取运营商信息
    String getOperator();

    // 获取IP地址
    String getIPAddress();

    // MQTT相关方法
    // 设置MQTT回调函数
    typedef void (*MqttCallback)(const char* topic, const char* payload);
    void setMqttCallback(MqttCallback callback);

    // 配置MQTT客户端
    bool mqttConfig(const char* clientId, const char* server, uint16_t port, const char* username = "", const char* password = "");

    // 连接MQTT服务器
    bool mqttConnect();

    // 断开MQTT连接
    bool mqttDisconnect();

    // 检查MQTT连接状态
    bool mqttIsConnected();

    // 发布MQTT消息
    bool mqttPublish(const char* topic, const char* payload, uint8_t qos = 0, bool retained = false);

    // 订阅MQTT主题
    bool mqttSubscribe(const char* topic, uint8_t qos = 0);

    // 取消订阅MQTT主题
    bool mqttUnsubscribe(const char* topic);

    // 处理MQTT事件 (保留但不使用)
    void mqttLoop();

    // HTTP相关方法
    // 发送HTTP GET请求
    String httpGet(const char* url, unsigned long timeout = 10000);

    // 发送HTTP POST请求
    String httpPost(const char* url, const char* contentType, const char* data, unsigned long timeout = 10000);

    // 时间同步相关方法
    // 配置NTP服务器
    bool configNTP(const char* server = "pool.ntp.org", int timezone = 8);

    // 同步网络时间
    bool syncNTP();

    // 获取当前时间
    bool getTime(int* year, int* month, int* day, int* hour, int* minute, int* second);

    // 获取当前Unix时间戳（秒）
    time_t getUnixTime();

private:
    // 等待特定响应
    bool waitForResponse(const char* expected, unsigned long timeout = 1000);

    // 解析AT命令响应
    String parseResponse(const String &response, const char* prefix);

    // 电源控制
    void powerOn();
    void powerOff();

    bool _isConnected;
    unsigned long _lastCommandTime;

    // MQTT相关成员变量
    bool _mqttConfigured;
    bool _mqttConnected;
    MqttCallback _mqttCallback;
    char _mqttClientId[64];
    char _mqttServer[64];
    uint16_t _mqttPort;
    char _mqttUsername[32];
    char _mqttPassword[32];
    unsigned long _lastMqttStatusCheck;

    // 不再需要MQTT消息缓冲区相关变量
};

#endif // AIR780E_H
