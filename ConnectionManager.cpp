#include "ConnectionManager.h"

ConnectionManager::ConnectionManager(HardwareNetworkManager* networkManager, MQTTClient* mqttClient)
    : _hardwareNetworkManager(networkManager),
      _mqttClient(mqttClient),
      _state(STATE_DISCONNECTED),
      _lastStateChangeTime(0),
      _lastNetworkConnectAttempt(0),
      _lastMqttConnectAttempt(0) {
}

bool ConnectionManager::begin() {
    // 初始化连接管理器
    _state = STATE_DISCONNECTED;
    _lastStateChangeTime = millis();

    return true;
}

void ConnectionManager::handleEvents() {
    // 处理连接事件
    unsigned long currentMillis = millis();

    // 更新连接状态 - 检查实际的硬件连接状态并更新状态机
    updateConnectionState();

    // 根据当前状态执行不同的操作
    switch (_state) {
        case STATE_DISCONNECTED:
            // 如果处于断开状态，尝试连接网络
            if (currentMillis - _lastNetworkConnectAttempt >= NETWORK_RECONNECT_INTERVAL) {
                setState(STATE_CONNECTING_NETWORK);
            }
            break;

        case STATE_CONNECTING_NETWORK:
            // 正在连接网络
            handleNetworkConnection();

            // 检查超时
            if (_state == STATE_CONNECTING_NETWORK && currentMillis - _lastStateChangeTime >= CONNECTION_TIMEOUT) {
                Serial.println("网络连接超时，重置状态");
                setState(STATE_DISCONNECTED);
                // 更新尝试时间，避免立即重试
                _lastNetworkConnectAttempt = currentMillis;
            }
            break;

        case STATE_NETWORK_CONNECTED:
            // 网络已连接，尝试连接MQTT
            if (currentMillis - _lastMqttConnectAttempt >= MQTT_RECONNECT_INTERVAL) {
                setState(STATE_CONNECTING_MQTT);
            }
            break;

        case STATE_CONNECTING_MQTT:
            // 正在连接MQTT
            handleMqttConnection();

            // 检查超时
            if (_state == STATE_CONNECTING_MQTT && currentMillis - _lastStateChangeTime >= CONNECTION_TIMEOUT) {
                Serial.println("MQTT连接超时，回到网络已连接状态");
                setState(STATE_NETWORK_CONNECTED);
                // 更新尝试时间，避免立即重试
                _lastMqttConnectAttempt = currentMillis;
            }
            break;

        case STATE_MQTT_CONNECTED:
            // MQTT已连接，不需要额外操作
            break;
    }
}

ConnectionState ConnectionManager::getState() {
    return _state;
}

bool ConnectionManager::isNetworkConnected() {
    return _state == STATE_NETWORK_CONNECTED ||
           _state == STATE_CONNECTING_MQTT ||
           _state == STATE_MQTT_CONNECTED;
}

bool ConnectionManager::isMqttConnected() {
    return _state == STATE_MQTT_CONNECTED;
}

void ConnectionManager::reconnectNetwork() {
    // 强制重新连接网络
    setState(STATE_DISCONNECTED);
    _lastNetworkConnectAttempt = 0; // 立即尝试连接
}

void ConnectionManager::reconnectMqtt() {
    // 强制重新连接MQTT
    if (_state == STATE_MQTT_CONNECTED || _state == STATE_CONNECTING_MQTT) {
        setState(STATE_NETWORK_CONNECTED);
        _lastMqttConnectAttempt = 0; // 立即尝试连接
    }
}

void ConnectionManager::disconnectAll() {
    // 断开所有连接
    _mqttClient->disconnect();
    _hardwareNetworkManager->disconnect();
    setState(STATE_DISCONNECTED);
}

void ConnectionManager::setStateChangeCallback(StateChangeCallback callback) {
    _stateChangeCallback = callback;
}

void ConnectionManager::setState(ConnectionState newState) {
    // 如果状态没有变化，不做任何操作
    if (_state == newState) {
        return;
    }

    // 保存旧状态
    ConnectionState oldState = _state;

    // 更新状态
    _state = newState;
    _lastStateChangeTime = millis();

    // 根据新状态执行相应操作
    switch (newState) {
        case STATE_DISCONNECTED:
            Serial.println("状态变化: 未连接");
            break;

        case STATE_CONNECTING_NETWORK:
            Serial.println("状态变化: 正在连接网络");
            _lastNetworkConnectAttempt = millis();
            break;

        case STATE_NETWORK_CONNECTED:
            Serial.println("状态变化: 网络已连接");
            break;

        case STATE_CONNECTING_MQTT:
            Serial.println("状态变化: 正在连接MQTT");
            _lastMqttConnectAttempt = millis();
            break;

        case STATE_MQTT_CONNECTED:
            Serial.println("状态变化: MQTT已连接");
            break;
    }

    // 调用状态变化回调
    if (_stateChangeCallback) {
        _stateChangeCallback(oldState, newState);
    }
}

void ConnectionManager::handleNetworkConnection() {
    // 首先检查当前状态，确保我们仍然处于连接网络状态
    if (_state != STATE_CONNECTING_NETWORK) {
        return;
    }

    // 检查网络是否已连接
    if (_hardwareNetworkManager->isConnected()) {
        // 网络已连接，更新状态
        Serial.println("网络已连接，更新状态");
        setState(STATE_NETWORK_CONNECTED);
        return;
    }

    // 检查是否应该尝试连接
    unsigned long currentMillis = millis();
    if (currentMillis - _lastNetworkConnectAttempt < NETWORK_RECONNECT_INTERVAL) {
        // 如果距离上次尝试的时间不够长，则不尝试连接
        return;
    }

    // 更新尝试连接时间
    _lastNetworkConnectAttempt = currentMillis;

    // 尝试连接网络
    Serial.println("尝试连接网络...");
    bool connectResult = _hardwareNetworkManager->connect();

    // 再次检查当前状态，确保状态没有被其他地方改变
    if (_state != STATE_CONNECTING_NETWORK) {
        Serial.println("状态已被其他地方改变，不更新连接结果");
        return;
    }

    if (connectResult) {
        // 网络连接成功，更新状态
        Serial.println("网络连接成功，更新状态");
        setState(STATE_NETWORK_CONNECTED);
    } else {
        Serial.println("网络连接失败，将在稍后重试");
        // 连接失败，回到断开状态，等待下一次尝试
        setState(STATE_DISCONNECTED);
    }
}

void ConnectionManager::updateConnectionState() {
    // 根据当前状态和实际连接状态更新状态机
    switch (_state) {
        case STATE_MQTT_CONNECTED:
            // 如果MQTT断开连接
            if (!_mqttClient->isConnected())
            {
                Serial.println("MQTT连接已断开");
                setState(STATE_NETWORK_CONNECTED);
            }
            // 如果网络断开连接
            else if (!_hardwareNetworkManager->isConnected())
            {
                Serial.println("网络连接已断开");
                setState(STATE_DISCONNECTED);
            }
            break;

        case STATE_NETWORK_CONNECTED:
            // 如果网络断开连接
            if (!_hardwareNetworkManager->isConnected())
            {
                Serial.println("网络连接已断开");
                setState(STATE_DISCONNECTED);
            }
            break;

        case STATE_CONNECTING_MQTT:
            // 在连接MQTT过程中，只检查网络连接状态
            if (!_hardwareNetworkManager->isConnected())
            {
                Serial.println("网络连接已断开");
                setState(STATE_DISCONNECTED);
            }
            break;

        case STATE_CONNECTING_NETWORK:
            // 在连接网络过程中
            break;

        case STATE_DISCONNECTED:
            // 在断开状态下，如果发现网络已连接，可以直接更新状态
            if (_hardwareNetworkManager->isConnected())
            {
                Serial.println("检测到网络已连接");
                setState(STATE_NETWORK_CONNECTED);
            }
            break;
    }
}

void ConnectionManager::handleMqttConnection() {
    // 首先检查当前状态，确保我们仍然处于连接MQTT状态
    if (_state != STATE_CONNECTING_MQTT) {
        return;
    }

    // 检查MQTT是否已连接
    if (_mqttClient->isConnected()) {
        // MQTT已连接，更新状态
        Serial.println("MQTT已连接，更新状态");
        setState(STATE_MQTT_CONNECTED);
        return;
    }

    // 检查是否应该尝试连接
    unsigned long currentMillis = millis();
    if (currentMillis - _lastMqttConnectAttempt < MQTT_RECONNECT_INTERVAL) {
        // 如果距离上次尝试的时间不够长，则不尝试连接
        return;
    }

    // 更新尝试连接时间
    _lastMqttConnectAttempt = currentMillis;

    // 尝试连接MQTT - 非阻塞操作
    Serial.println("尝试连接MQTT...");
    bool connectResult = _mqttClient->connect();

    // 不管连接结果如何，都立即返回，避免阻塞
    // 连接状态将在下一次循环中通过isConnected()检查

    // 如果连接命令发送失败，回到网络已连接状态
    if (!connectResult) {
        Serial.println("MQTT连接命令发送失败，将在稍后重试");
        setState(STATE_NETWORK_CONNECTED);
    } else {
        Serial.println("MQTT连接命令已发送，等待连接结果");
        // 保持在STATE_CONNECTING_MQTT状态，等待下一次循环检查连接结果
    }
}
