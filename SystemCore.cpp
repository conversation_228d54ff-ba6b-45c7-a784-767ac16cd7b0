#include "SystemCore.h"

// 全局系统核心实例
SystemCore systemCore;

// 获取单例实例
SystemCore* SystemCore::getInstance() {
    return &systemCore;
}

SystemCore::SystemCore()
    : _hardwareNetworkManager(&_air780e),
      _mqttClient(&_hardwareNetworkManager, &_air780e),
      _webServer(&_hardwareNetworkManager, &_mqttClient, &_deviceControl, &_otaManager),
      _connectionManager(&_hardwareNetworkManager, &_mqttClient),
      _buttonPressed(false),
      _buttonPressStartTime(0),
      _webServerRunning(false),
      _timeIsSynced(false),
      _lastTimeSyncAttempt(0)
{
}

bool SystemCore::begin()
{
    // 初始化串口
    Serial.begin(115200);
    Serial.println("\n\n自助贩卖机系统启动中...");

    // 初始化设备控制
    if (_deviceControl.begin())
    {
        Serial.println("设备控制初始化成功");
    }
    else
    {
        Serial.println("设备控制初始化失败");
    }

    // 初始化4G模块
    if (_air780e.begin())
    {
        Serial.println("4G模块初始化成功");
        delay(3000);

        // 获取IMEI
        String imei = _air780e.getIMEI();
        Serial.println("IMEI: " + imei);

        // 获取ICCID
        String iccid = _air780e.getICCID();
        Serial.println("ICCID: " + iccid);

        // 获取运营商
        String oper = _air780e.getOperator();
        Serial.println("运营商: " + oper);

        // 开启GPS
        _air780e.gpsOpen();
    }
    else
    {
        Serial.println("4G模块重新初始化失败");
    }

    // 初始化网络管理器
    if (_hardwareNetworkManager.begin())
    {
        Serial.println("网络管理器初始化成功");
    }
    else
    {
        Serial.println("网络管理器初始化失败");
    }

    // 初始化Web服务器
    if (_webServer.begin())
    {
        Serial.println("Web服务器初始化成功");
    }
    else
    {
        Serial.println("Web服务器初始化失败");
    }

    // 初始化MQTT客户端
    if (_mqttClient.begin())
    {
        Serial.println("MQTT客户端初始化成功");
    }
    else
    {
        Serial.println("MQTT客户端初始化失败");
    }

    // 初始化命令处理器
    if (_commandProcessor.begin(&_mqttClient, &_deviceControl, &_air780e, &_statusManager, &_hardwareNetworkManager, &_webServer))
    {
        // 设置MQTT回调
        _mqttClient.setCallback(CommandProcessor::mqttCallback);
        Serial.println("命令处理器初始化成功");
    }
    else
    {
        Serial.println("命令处理器初始化失败");
    }

    // 初始化状态管理器
    if (_statusManager.begin(&_mqttClient, &_deviceControl, &_air780e, &_hardwareNetworkManager, &_connectionManager, &_webServer))
    {
        Serial.println("状态管理器初始化成功");
    }
    else
    {
        Serial.println("状态管理器初始化失败");
    }

    // 初始化蓝牙管理器
    // if (_bluetoothManager.begin(&_webServer.getConfig(), &_statusManager.getDeviceStatus(), &_deviceControl, &_statusManager))
    // {
    //     Serial.println("蓝牙管理器初始化成功");
    // }
    // else
    // {
    //     Serial.println("蓝牙管理器初始化失败");
    // }

    // 初始化连接管理器：网络，MQTT 连接等检测
    if (_connectionManager.begin())
    {
        // 设置连接状态变化回调
        _connectionManager.setStateChangeCallback([this](ConnectionState oldState, ConnectionState newState)
                                                  { this->onConnectionStateChange(this, oldState, newState); });
        Serial.println("连接管理器初始化成功");
    }
    else
    {
        Serial.println("连接管理器初始化失败");
    }

    // 从 Web 服务器管理器中获取配置并应用
    Serial.println("应用配置文件中的网络设置...");
    _webServer.applyConfig();

    Serial.println("系统初始化完成");
    return true;
}

void SystemCore::loop()
{
    // 处理硬件网络事件 - 只处理状态更新，不处理连接管理
    _hardwareNetworkManager.handleEvents();

    // 处理连接管理 - 集中管理网络和MQTT连接
    _connectionManager.handleEvents();

    // 定期同步时间
    unsigned long currentMillis = millis();
    if ((currentMillis - _lastTimeSyncAttempt > TIME_SYNC_INTERVAL || (currentMillis - _lastTimeSyncAttempt > TIME_SYNC_INITIAL_DELAY && !_timeIsSynced))
         && _hardwareNetworkManager.isConnected())
    {
        syncNetworkTime();
    }

    // 管理Web服务器状态
    manageWebServerState();

    // 处理MQTT事件 - 只处理消息接收，不处理连接管理
    _mqttClient.handleEvents();

    // 处理设备事件
    _deviceControl.handleEvents();

    // 处理命令处理器事件 - 检查待发送的出货结果
    _commandProcessor.handleEvents();

    // 处理OTA事件
    _otaManager.handleEvents();

    // 只有在Web服务器运行时才处理Web服务器事件
    if (_webServerRunning)
    {
        _webServer.handleEvents();
    }

    // 处理状态管理器事件
    _statusManager.handleEvents();

    // 处理蓝牙事件
    // _bluetoothManager.handleEvents();
    // 获取设备状态（通过allowScan判断）
    // bool deviceStatus = _deviceControl.getDeviceStatus();
    // 每30秒打印一次设备状态
    // static unsigned long lastStatusPrintTime = 0;
    // unsigned long currentTime = millis();
    // if (currentTime - lastStatusPrintTime > 30000) {
    //     lastStatusPrintTime = currentTime;
    //     Serial.printf("设备状态: %s\n", deviceStatus ? "正常" : "异常");
    // }

    // 短暂延迟
    delay(10);
}

// 测试代码
void SystemCore::handleResetButton()
{
    // 读取按键状态（低电平为按下状态）
    bool currentState = digitalRead(PIN_RESET_BUTTON) == LOW;

    // 按键按下
    if (currentState && !_buttonPressed)
    {
        _buttonPressed = true;
        _buttonPressStartTime = millis();
        Serial.println("按键按下，开始计时");
    }
    // 按键释放
    else if (!currentState && _buttonPressed)
    {
        _buttonPressed = false;
        Serial.println("按键释放，按下时间: " + String((millis() - _buttonPressStartTime) / 1000) + "秒");
    }
    // 按键长按检测
    if (_buttonPressed && (millis() - _buttonPressStartTime > LONG_PRESS_TIME))
    {
        // 长按超过10秒，进入AP模式
        _buttonPressed = false; // 重置按键状态，避免重复触发
        // enterAPMode();
    }
}

// 测试代码
void SystemCore::enterAPMode()
{
    Serial.println("检测到长按超过10秒，进入AP模式");

    // 断开所有连接
    // _connectionManager.disconnectAll();

    // 获取WebServerManager的配置
    SystemConfig config = _webServer.getConfig();

    // 启动AP模式，使用配置中的SSID和密码
    if (_hardwareNetworkManager.startAP(config.web_server_ssid, config.web_server_password))
    {
        Serial.println("已启动AP模式，请连接到WiFi: " + String(config.web_server_ssid));
        Serial.println("然后访问 http://192.168.4.1 进行配置");

        // Web服务器将在loop()中检测到AP模式后自动启动
    }
    else
    {
        Serial.println("启动AP模式失败");
    }
}

void SystemCore::restart()
{
    Serial.println("系统即将重启...");
    delay(1000);
    ESP.restart();
}

void SystemCore::manageWebServerState()
{
    // 检查AP模式状态
    bool apActive = _hardwareNetworkManager.isAPActive();

    // 根据AP模式状态管理Web服务器
    if (apActive && !_webServerRunning)
    {
        // AP模式已开启但Web服务器未运行，启动Web服务器
        Serial.println("检测到AP模式已开启，启动Web服务器...");
        _webServer.start();
        _webServerRunning = true;
    }
}

bool SystemCore::syncNetworkTime()
{
    if (!_hardwareNetworkManager.isConnected()) {
        // Serial.println("无法同步时间: 网络未连接");
        return false;
    }

    Serial.println("开始同步网络时间...");

    // 根据网络模式选择不同的时间同步方式
    if (_hardwareNetworkManager.getNetworkMode() == NETWORK_MODE_4G) {
        // 4G模式下使用Air780E的NTP功能
        Serial.println("使用4G模块同步网络时间...");
        _lastTimeSyncAttempt = millis();

        // 配置NTP服务器
        if (!_air780e.configNTP("ntp.aliyun.com", 32)) {
            _timeIsSynced = false;
            return false;
        }

        // 同步网络时间
        if (!_air780e.syncNTP()) {
            _timeIsSynced = false;
            return false;
        }

        // 获取并打印当前时间
        int year, month, day, hour, minute, second;
        if (!_air780e.getTime(&year, &month, &day, &hour, &minute, &second)) {
            _timeIsSynced = false;
            return false;
        }

        // 设置系统时间
        struct tm timeinfo;
        timeinfo.tm_year = year - 1900;
        timeinfo.tm_mon = month - 1;
        timeinfo.tm_mday = day;
        timeinfo.tm_hour = hour;
        timeinfo.tm_min = minute;
        timeinfo.tm_sec = second;
        time_t t = mktime(&timeinfo);
        struct timeval now = { .tv_sec = t };
        settimeofday(&now, NULL);

        Serial.printf("当前时间: %04d-%02d-%02d %02d:%02d:%02d\n",
                     year, month, day, hour, minute, second);

        _timeIsSynced = true;

        return true;
    } else {
        // WiFi模式下使用ESP32的NTP功能
        Serial.println("使用WiFi同步网络时间...");

        // 配置NTP服务器
        configTime(8 * 3600, 0, "pool.ntp.org", "time.nist.gov", "time.windows.com");

        // 等待时间同步
        Serial.println("等待时间同步...");
        time_t now = time(nullptr);
        int retries = 0;
        const int maxRetries = 10;

        while (now < 24 * 3600 && retries < maxRetries) {
            Serial.print(".");
            delay(500);
            now = time(nullptr);
            retries++;
        }

        if (now < 24 * 3600) {
            Serial.println("\n时间同步失败");
            _timeIsSynced = false;
            return false;
        }

        Serial.println("\n时间同步成功");

        // 获取并打印当前时间
        struct tm timeinfo;
        getLocalTime(&timeinfo);
        char timeStr[64];
        strftime(timeStr, sizeof(timeStr), "%Y-%m-%d %H:%M:%S", &timeinfo);
        Serial.printf("当前时间: %s\n", timeStr);

        _timeIsSynced = true;
        _lastTimeSyncAttempt = millis();

        return true;
    }
}

time_t SystemCore::getCurrentUnixTime()
{
    // 根据网络模式选择不同的获取时间方式
    if (_hardwareNetworkManager.getNetworkMode() == NETWORK_MODE_4G && _hardwareNetworkManager.isConnected()) {
        // 4G模式下使用Air780E的时间功能
        time_t t = _air780e.getUnixTime();
        if (t > 0) {
            return t;
        }
        // 如果获取失败，回退到系统时间
        Serial.println("4G获取时间失败，使用系统时间");
    }

    // 返回当前Unix时间戳（秒）
    return time(nullptr);
}

uint64_t SystemCore::getCurrentUnixTimeMs()
{
    // 获取当前Unix时间戳（秒）
    time_t seconds = getCurrentUnixTime();

    // 获取毫秒部分
    struct timeval tv;
    gettimeofday(&tv, nullptr);

    // 返回当前Unix时间戳（毫秒）
    return (uint64_t)seconds * 1000 + (tv.tv_usec / 1000);
}

String SystemCore::getCurrentTimeString(const char* format)
{
    // 获取当前Unix时间戳
    time_t now = getCurrentUnixTime();

    if (now < 24 * 3600) // 如果时间未同步（1970年1月1日之后的24小时内）
    {
        return "时间未同步";
    }

    // 转换为本地时间（中国时区 UTC+8）
    struct tm timeinfo;
    localtime_r(&now, &timeinfo);

    // 格式化时间字符串
    char buffer[64];
    strftime(buffer, sizeof(buffer), format, &timeinfo);

    return String(buffer);
}

void SystemCore::onConnectionStateChange(void *instance, ConnectionState oldState, ConnectionState newState)
{
    SystemCore *core = static_cast<SystemCore *>(instance);

    // 根据新状态执行相应操作
    switch (newState)
    {
    case STATE_DISCONNECTED:
        Serial.println("连接状态: 未连接");
        break;

    case STATE_CONNECTING_NETWORK:
        Serial.println("连接状态: 正在连接网络");
        break;

    case STATE_NETWORK_CONNECTED:
        Serial.println("连接状态: 网络已连接");
        // 网络连接成功后可以执行一些操作
        if (core->_hardwareNetworkManager.getNetworkMode() == NETWORK_MODE_WIFI && WiFi.status() == WL_CONNECTED)
        {
            Serial.println("WiFi IP地址: " + WiFi.localIP().toString());
        }
        else if (core->_hardwareNetworkManager.getNetworkMode() == NETWORK_MODE_4G)
        {
            Serial.println("4G网络已连接");
        }
        break;

    case STATE_CONNECTING_MQTT:
        Serial.println("连接状态: 正在连接MQTT");
        break;

    case STATE_MQTT_CONNECTED:
        Serial.println("连接状态: MQTT已连接");
        // MQTT连接成功后可以执行一些操作，如订阅主题
        break;
    }
}
