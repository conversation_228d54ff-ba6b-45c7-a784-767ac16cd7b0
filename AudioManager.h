#ifndef AUDIO_MANAGER_H
#define AUDIO_MANAGER_H

#include <Arduino.h>
#include <Preferences.h>
#include <LittleFS.h>
#include "Audio.h"  // ESP32-audioI2S库
#include "config.h"

// 音频播放模式
enum AudioPlayMode {
    AUDIO_MODE_URL = 0,     // URL流播放模式
    AUDIO_MODE_LOCAL = 1    // 本地文件播放模式
};

// 音频文件结构
struct AudioFile {
    const char* name;       // 音频名称
    const char* nvs_key;    // NVS存储键名
    String url;             // 音频文件URL
    String localPath;       // 本地文件路径
    size_t size;            // 音频数据大小
    bool loaded;            // 是否已加载
    AudioPlayMode mode;     // 播放模式
};

// 预定义音频文件
enum AudioFileIndex {
    AUDIO_WELCOME = 0,    // 欢迎语音
    AUDIO_THANK_YOU,      // 感谢语音
    AUDIO_ERROR,          // 错误提示音
    AUDIO_COUNT           // 音频文件总数
};

class AudioManager {
public:
    AudioManager();
    ~AudioManager();

    // 初始化音频管理器
    bool begin();

    // 播放欢迎语音
    bool playWelcome();

    // 播放指定索引的音频
    bool playAudio(uint8_t index);

    // 停止播放
    bool stopPlay();

    // 设置音量（0-100）
    bool setVolume(uint8_t volume);

    // 获取当前音量
    uint8_t getVolume() const;

    // 加载音频数据
    bool loadAudioData();

    // 保存音频URL到NVS
    bool saveAudioURL(uint8_t index, const String& url);

    // 从URL播放音频
    bool playAudioFromURL(uint8_t index, const String& url);

    // 下载音频文件到本地存储
    bool downloadAudioFile(uint8_t index, const String& url);

    // 从本地文件播放音频
    bool playLocalAudio(uint8_t index);

    // 删除本地音频文件
    bool deleteLocalAudio(uint8_t index);

    // 检查本地音频文件是否存在
    bool localAudioExists(uint8_t index);

    // 获取本地音频文件大小
    size_t getLocalAudioSize(uint8_t index);

    // 检查音频是否正在播放
    bool isPlaying();

    // 处理事件（在主循环中调用）
    void handleEvents();

private:
    Audio _audio;                    // ESP32-audioI2S库实例
    uint8_t _volume;
    bool _initialized;
    bool _playing;
    uint8_t _currentAudio;
    Preferences _preferences;
    AudioFile _audioFiles[AUDIO_COUNT];

    // 初始化I2S和Audio库
    bool initAudio();

    // 初始化默认音频URL
    void initDefaultAudio();

    // 从NVS加载音频URL
    bool loadAudioFromNVS(uint8_t index);

    // 保存音频URL到NVS
    bool saveAudioToNVS(uint8_t index, const String& url);

    // 初始化LittleFS文件系统
    bool initFileSystem();

    // 生成本地音频文件路径
    String generateLocalPath(uint8_t index);

    // 下载文件的内部实现
    bool downloadFileInternal(const String& url, const String& localPath);
};

#endif // AUDIO_MANAGER_H
