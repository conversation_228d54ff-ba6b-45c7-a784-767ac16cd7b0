#include "MQTTClient.h"
#include <ArduinoJson.h>

// 初始化静态实例指针
MQTTClient* MQTTClient::_instance = nullptr;

MQTTClient::MQTTClient(HardwareNetworkManager* networkManager, Air780E* air780e)
    : _hardwareNetworkManager(networkManager),
      _air780e(air780e),
      _mqttClient(_wifiClient),
      _port(1883),
      _lastConnected(false),
      _lastConnectedTime(0),
      _use4GMQTT(false),
      _4GMQTTConfigured(false),
      _4GMQTTConnected(false) {

    // 初始化配置
    memset(_server, 0, sizeof(_server));
    memset(_username, 0, sizeof(_username));
    memset(_password, 0, sizeof(_password));
    memset(_clientId, 0, sizeof(_clientId));
    memset(_statusTopic, 0, sizeof(_statusTopic));
    memset(_commandTopic, 0, sizeof(_commandTopic));

    // 设置静态实例指针
    _instance = this;
}

bool MQTTClient::begin() {
    // 设置MQTT回调
    _mqttClient.setCallback(_mqttCallback);

    // 获取设备唯一ID作为标识符
    String deviceId = _hardwareNetworkManager->getUniqueDeviceId();

    // 设置默认服务器
    strncpy(_server, DEFAULT_MQTT_SERVER, sizeof(_server) - 1);
    _port = DEFAULT_MQTT_PORT;
    strncpy(_username, DEFAULT_MQTT_USER, sizeof(_username) - 1);
    strncpy(_password, DEFAULT_MQTT_PASSWORD, sizeof(_password) - 1);

    // 使用设备名称和设备唯一ID作为主题标识符
    String deviceName = String(DEVICE_NAME);

    // 构建状态主题: 设备名称/DEFAULT_MQTT_TOPIC_STATUS/设备ID
    String statusTopicWithId = deviceName + "/" + String(DEFAULT_MQTT_TOPIC_STATUS) + "/" + deviceId + "/cabinet";

    // 构建命令主题: 设备名称/DEFAULT_MQTT_TOPIC_COMMAND/设备ID
    String commandTopicWithId = deviceName + "/" + String(DEFAULT_MQTT_TOPIC_COMMAND) + "/" + deviceId;

    strncpy(_statusTopic, statusTopicWithId.c_str(), sizeof(_statusTopic) - 1);
    strncpy(_commandTopic, commandTopicWithId.c_str(), sizeof(_commandTopic) - 1);

    Serial.printf("MQTT主题: 状态=%s, 命令=%s\n",
                 _statusTopic, _commandTopic);

    return true;
}

bool MQTTClient::configure(const char* server, uint16_t port, const char* username, const char* password, const char* clientId) {
    // 保存配置
    strncpy(_server, server, sizeof(_server) - 1);
    _port = port;
    strncpy(_username, username, sizeof(_username) - 1);
    strncpy(_password, password, sizeof(_password) - 1);
    strncpy(_clientId, clientId, sizeof(_clientId) - 1);

    // 如果已连接，断开并重新连接
    if (_mqttClient.connected()) {
        disconnect();
        return connect();
    }

    return true;
}

void MQTTClient::setTopics(const char* statusTopic, const char* commandTopic) {
    strncpy(_statusTopic, statusTopic, sizeof(_statusTopic) - 1);
    strncpy(_commandTopic, commandTopic, sizeof(_commandTopic) - 1);

    // 如果已连接，重新订阅命令主题
    if (_mqttClient.connected()) {
        _mqttClient.unsubscribe(_commandTopic);
        _mqttClient.subscribe(_commandTopic);
    }
}

bool MQTTClient::connect() {
    // 检查网络连接
    if (!_hardwareNetworkManager->isConnected()) {
        Serial.println("MQTT连接失败: 网络未连接");
        return false;
    }

    // 检查服务器配置
    if (strlen(_server) == 0) {
        Serial.println("MQTT连接失败: 服务器地址为空");
        return false;
    }

    // 检查客户端ID
    if (strlen(_clientId) == 0) {
        Serial.println("MQTT连接失败: 客户端ID为空");
        return false;
    }

    // 根据网络模式选择不同的连接方式
    if (_hardwareNetworkManager->getNetworkMode() == NETWORK_MODE_4G) {
        // 4G模式使用4G MQTT
        Serial.println("使用4G MQTT连接");
        _use4GMQTT = true;
        return connect4GMQTT();
    } else {
        // WiFi模式使用普通MQTT
        Serial.println("使用WiFi MQTT连接");
        _use4GMQTT = false;

        // 打印连接信息
        Serial.printf("MQTT连接到服务器: %s:%d, 客户端ID: %s\n", _server, _port, _clientId);
        if (strlen(_username) > 0) {
            Serial.printf("MQTT认证: 用户名=%s, 密码长度=%d\n", _username, strlen(_password));
        } else {
            Serial.println("MQTT连接: 不使用认证");
        }

        // 设置服务器
        _mqttClient.setServer(_server, _port);

        // 连接到MQTT服务器
        bool result = false;
        if (strlen(_username) > 0) {
            result = _mqttClient.connect(_clientId, _username, _password);
        } else {
            result = _mqttClient.connect(_clientId);
        }

        // 检查连接结果
        if (result) {
            Serial.println("MQTT连接成功");

            // 订阅命令主题
            if (strlen(_commandTopic) > 0) {
                bool subscribed = _mqttClient.subscribe(_commandTopic);
                Serial.printf("MQTT订阅命令主题 '%s' %s\n", _commandTopic, subscribed ? "成功" : "失败");
            } else {
                Serial.println("MQTT命令主题为空，跳过订阅");
            }
        } else {
            Serial.println("MQTT连接失败");
        }

        return result;
    }
}

void MQTTClient::disconnect() {
    if (_use4GMQTT) {
        // 4G MQTT断开连接
        disconnect4GMQTT();
    } else {
        // WiFi MQTT断开连接
        _mqttClient.disconnect();
    }
}

bool MQTTClient::isConnected() {
    bool currentlyConnected = false;

    if (_use4GMQTT) {
        // 4G MQTT检查连接状态
        currentlyConnected = is4GMQTTConnected();
    } else {
        // WiFi MQTT检查连接状态
        currentlyConnected = _mqttClient.connected();

        // 如果连接状态发生变化
        if (currentlyConnected != _lastConnected) {
            // 如果新连接成功
            if (currentlyConnected) {
                _lastConnected = true;
                _lastConnectedTime = millis();
                Serial.println("MQTT连接成功");
            }
            // 如果断开连接
            else {
                _lastConnected = false;
                Serial.println("MQTT连接断开");
            }
        }
    }

    // 调试输出
    // Serial.printf("MQTT连接状态: %s\n", currentlyConnected ? "已连接" : "未连接");

    return currentlyConnected;
}

bool MQTTClient::publish(const char* topic, const char* payload, uint8_t qos) {
    if (_use4GMQTT) {
        // 4G MQTT发布消息
        return publish4GMQTT(topic, payload, qos);
    } else {
        // WiFi MQTT发布消息
        if (!_mqttClient.connected()) {
            return false;
        }

        // 获取消息长度
        size_t payloadLength = strlen(payload);

        // 打印消息长度
        Serial.printf("发布消息到 %s，消息长度: %d 字节\n", topic, payloadLength);
        Serial.print("消息内容: ");
        Serial.println(payload);

        // 尝试发布消息
        bool result = _mqttClient.publish(topic, (const uint8_t*)payload, payloadLength, false);

        // 检查发布结果
        if (result) {
            Serial.println("消息发布成功");
        } else {
            Serial.println("消息发布失败");

            // 如果消息长度超过一定大小，尝试分片发送
            if (payloadLength > 200) {
                Serial.println("尝试分片发送大消息...");

                // 分片大小
                const size_t chunkSize = 200;

                // 分片数量
                size_t numChunks = (payloadLength + chunkSize - 1) / chunkSize;

                // 创建分片主题
                char chunkTopic[128];

                // 发送分片数量信息
                sprintf(chunkTopic, "%s/chunks", topic);
                char chunkInfo[32];
                sprintf(chunkInfo, "%d", numChunks);
                _mqttClient.publish(chunkTopic, (const uint8_t*)chunkInfo, strlen(chunkInfo), false);

                // 逐个发送分片
                bool allChunksSuccess = true;
                for (size_t i = 0; i < numChunks; i++) {
                    // 计算当前分片的起始位置和长度
                    size_t start = i * chunkSize;
                    size_t length = (i == numChunks - 1) ? (payloadLength - start) : chunkSize;

                    // 创建分片主题
                    sprintf(chunkTopic, "%s/chunk/%d", topic, i);

                    // 创建分片内容
                    char* chunk = new char[length + 1];
                    memcpy(chunk, payload + start, length);
                    chunk[length] = '\0';

                    // 发送分片
                    Serial.printf("发送分片 %d/%d，长度: %d 字节\n", i + 1, numChunks, length);
                    bool chunkResult = _mqttClient.publish(chunkTopic, (const uint8_t*)chunk, length, false);

                    // 释放内存
                    delete[] chunk;

                    // 检查分片发送结果
                    if (chunkResult) {
                        Serial.printf("分片 %d 发送成功\n", i + 1);
                    } else {
                        Serial.printf("分片 %d 发送失败\n", i + 1);
                        allChunksSuccess = false;
                    }

                    // 等待一小段时间，避免发送过快
                    delay(50);
                }

                // 返回分片发送结果
                return allChunksSuccess;
            }
        }

        return result;
    }
}

bool MQTTClient::subscribe(const char* topic) {
    if (_use4GMQTT) {
        // 4G MQTT订阅主题
        return subscribe4GMQTT(topic);
    } else {
        // WiFi MQTT订阅主题
        // 检查是否处于WiFi暂停状态
        bool wifiPaused = _hardwareNetworkManager->getNetworkMode() == NETWORK_MODE_WIFI &&
                         !_hardwareNetworkManager->getIPAddress().isEmpty() &&
                         _hardwareNetworkManager->getIPAddress().indexOf("(暂停)") > 0;

        // 如果是WiFi暂停状态且之前有连接
        if (wifiPaused && _lastConnected) {
            // 在WiFi暂停状态下不订阅主题，但返回成功
            Serial.printf("WiFi暂停状态，暂不订阅MQTT主题: %s\n", topic);
            return true;
        }

        if (!_mqttClient.connected()) {
            return false;
        }

        return _mqttClient.subscribe(topic);
    }
}

bool MQTTClient::unsubscribe(const char* topic) {
    if (_use4GMQTT) {
        // 4G MQTT取消订阅主题
        return unsubscribe4GMQTT(topic);
    } else {
        // WiFi MQTT取消订阅主题
        // 检查是否处于WiFi暂停状态
        bool wifiPaused = _hardwareNetworkManager->getNetworkMode() == NETWORK_MODE_WIFI &&
                         !_hardwareNetworkManager->getIPAddress().isEmpty() &&
                         _hardwareNetworkManager->getIPAddress().indexOf("(暂停)") > 0;

        // 如果是WiFi暂停状态且之前有连接
        if (wifiPaused && _lastConnected) {
            // 在WiFi暂停状态下不取消订阅主题，但返回成功
            Serial.printf("WiFi暂停状态，暂不取消订阅MQTT主题: %s\n", topic);
            return true;
        }

        if (!_mqttClient.connected()) {
            return false;
        }

        return _mqttClient.unsubscribe(topic);
    }
}

// 4G MQTT回调处理
void MQTTClient::_4gMqttCallback(const char* topic, const char* payload) {
    // 确保实例存在
    if (_instance == nullptr) {
        return;
    }

    // 调用用户回调
    if (_instance->_callback) {
        _instance->_callback(String(topic), String(payload));
    } else {
        Serial.println("警告: MQTT回调函数未设置，无法处理消息");
    }
}

void MQTTClient::setCallback(MQTTCallback callback) {
    _callback = callback;

    // 如果使用4G MQTT，设置Air780E的回调函数
    if (_air780e != nullptr) {
        // 设置4G MQTT回调函数
        _air780e->setMqttCallback(_4gMqttCallback);

        Serial.println("已设置4G MQTT回调函数");
    }
}

void MQTTClient::handleEvents() {
    // 处理MQTT事件 - 只处理消息接收，不处理连接管理

    // 根据网络模式选择不同的事件处理方式
    if (_hardwareNetworkManager->getNetworkMode() == NETWORK_MODE_4G) {
        // 4G模式使用4G MQTT
        _use4GMQTT = true;

        // 只处理消息接收，不处理连接管理
        if (_4GMQTTConnected) {
            _air780e->mqttLoop();
        }
    } else {
        // WiFi模式使用普通MQTT
        _use4GMQTT = false;

        // 只处理消息接收，不处理连接管理
        if (_mqttClient.connected()) {
            _mqttClient.loop();
        }
    }
}

bool MQTTClient::publishStatus(const char* topicCode, const DeviceStatus& status) {
    // 创建JSON文档
    StaticJsonDocument<512> doc;

    doc["topicCode"] = topicCode;

    // 添加设备状态信息
    doc["wifi_connected"] = status.wifi_connected;
    doc["cellular_connected"] = status.cellular_connected;
    doc["mqtt_connected"] = status.mqtt_connected;
    doc["motor_running"] = status.motor_running;

    // 添加红外传感器状态
    doc["ir_triggered"] = status.ir_triggered;
    doc["ir_triggered2"] = status.ir_triggered2;
    doc["ir_triggered3"] = status.ir_triggered3;
    doc["pir_triggered"] = status.pir_triggered;

    // 添加继电器状态
    JsonArray relays = doc.createNestedArray("relays");
    for (int i = 0; i < 5; i++) {
        relays.add(status.relay_status[i]);
    }

    doc["uptime"] = status.uptime;
    doc["signal_strength"] = status.signal_strength;

    // 添加设备通信状态和设备状态
    doc["device_communication_error"] = status.device_communication_error;
    doc["device_status"] = status.device_status;
    doc["query_timeout_count"] = status.query_timeout_count;

    // 添加位置信息
    if (status.latitude != 0 || status.longitude != 0) {
        doc["latitude"] = status.latitude;
        doc["longitude"] = status.longitude;
        doc["location_update"] = status.last_location_update;
    }

    // 添加设备信息
    doc["firmware"] = FIRMWARE_VERSION;
    doc["device_name"] = DEVICE_NAME;
    doc["mac_address"] = status.mac_address;

    // 添加网络模式
    // doc["network_mode"] = _hardwareNetworkManager->getNetworkMode() == NETWORK_MODE_WIFI ? "WiFi" : "4G";

    // 添加IP地址
    // doc["ip_address"] = _hardwareNetworkManager->getIPAddress();

    // 序列化JSON
    String payload;
    serializeJson(doc, payload);

    // 打印发布的消息内容和长度
    Serial.print("发布的消息内容: ");
    Serial.println(payload);
    Serial.printf("发布的消息长度: %d\n", payload.length());

    // 发布状态
    return publish(_statusTopic, payload.c_str(), 0);
}

bool MQTTClient::publishLocation(const char* topicCode, float latitude, float longitude) {
    // 创建JSON文档
    StaticJsonDocument<256> doc;

    doc["topicCode"] = topicCode;
    // 添加位置信息
    doc["latitude"] = latitude;
    doc["longitude"] = longitude;
    // doc["timestamp"] = millis();

    // 序列化JSON
    String payload;
    serializeJson(doc, payload);

    // 发布位置到状态主题
    return publish(_statusTopic, payload.c_str(), 0);
}

bool MQTTClient::publishParams(const char* topicCode, const SystemConfig& config) {
    // 创建JSON文档
    StaticJsonDocument<1024> paramsDoc;

    paramsDoc["topicCode"] = topicCode;

    // 添加MQTT配置
    paramsDoc["mqtt_broker"] = config.mqtt_server;
    paramsDoc["mqtt_port"] = config.mqtt_port;
    paramsDoc["mqtt_username"] = config.mqtt_user;
    paramsDoc["mqtt_password"] = config.mqtt_password;

    // 添加WiFi配置
    paramsDoc["wifi_ssid"] = config.wifi_ssid;
    paramsDoc["wifi_password"] = config.wifi_password;
    paramsDoc["web_server_ssid"] = config.web_server_ssid;
    paramsDoc["web_server_password"] = config.web_server_password;

    // 添加API接口配置
    paramsDoc["api_goodsOut_url"] = config.api_goodsOut;
    paramsDoc["api_cabinetBinTest_url"] = config.api_cabinetBinTest;

    // 序列化JSON
    String payload;
    serializeJson(paramsDoc, payload);

    // 发布参数
    return publish(_statusTopic, payload.c_str(), 0);
}

// 发布_statusTopic主题的函数
bool MQTTClient::publishStatusTopic(String payload, uint8_t qos) {
    // 确保实例存在
    if (_instance == nullptr) {
        return false;
    }

    return publish(_statusTopic, payload.c_str(), qos);
}

void MQTTClient::_mqttCallback(char *topic, uint8_t *payload, unsigned int length)
{
    // 确保实例存在
    if (_instance == nullptr) {
        return;
    }

    // 转换payload为字符串
    String message;
    for (unsigned int i = 0; i < length; i++) {
        message += (char)payload[i];
    }

    // 调用用户回调
    if (_instance->_callback) {
        _instance->_callback(String(topic), message);
    }
}

// 4G MQTT相关方法实现

// 配置4G MQTT
bool MQTTClient::configure4GMQTT() {
    // 检查网络模式
    if (_hardwareNetworkManager->getNetworkMode() != NETWORK_MODE_4G) {
        Serial.println("4G MQTT配置失败: 不是4G网络模式");
        return false;
    }

    // 检查服务器配置
    if (strlen(_server) == 0) {
        Serial.println("4G MQTT配置失败: 服务器地址为空");
        return false;
    }

    // 检查客户端ID
    if (strlen(_clientId) == 0) {
        Serial.println("4G MQTT配置失败: 客户端ID为空");
        return false;
    }

    // 打印配置信息
    Serial.printf("4G MQTT配置: 服务器=%s:%d, 客户端ID=%s\n", _server, _port, _clientId);
    if (strlen(_username) > 0) {
        Serial.printf("4G MQTT认证: 用户名=%s, 密码长度=%d\n", _username, strlen(_password));
    } else {
        Serial.println("4G MQTT连接: 不使用认证");
    }

    // 设置4G MQTT回调函数
    _air780e->setMqttCallback(_4gMqttCallback);

    Serial.println("已设置4G MQTT回调函数");

    // 配置4G MQTT
    bool result = _air780e->mqttConfig(_clientId, _server, _port, _username, _password);
    if (result) {
        _4GMQTTConfigured = true;
        Serial.println("4G MQTT配置成功");
    } else {
        Serial.println("4G MQTT配置失败");
    }

    return result;
}

// 连接4G MQTT
bool MQTTClient::connect4GMQTT() {
    // 检查网络连接
    if (!_hardwareNetworkManager->isConnected()) {
        Serial.println("4G MQTT连接失败: 网络未连接");
        return false;
    }

    // 检查网络模式
    if (_hardwareNetworkManager->getNetworkMode() != NETWORK_MODE_4G) {
        Serial.println("4G MQTT连接失败: 不是4G网络模式");
        return false;
    }

    // 检查是否已配置，或者强制重新配置
    // 每次连接都重新配置，确保客户端ID等信息正确
    Serial.println("4G MQTT连接: 配置MQTT参数");
    if (!configure4GMQTT()) {
        Serial.println("4G MQTT连接失败: 配置失败");
        return false;
    }

    // 非阻塞连接，只尝试一次
    bool result = _air780e->mqttConnect();
    if (result) {
        _4GMQTTConnected = true;
        _use4GMQTT = true;
        Serial.println("4G MQTT连接成功");

        // 不再等待，立即返回结果
        // 连接状态将在后续的状态检查中更新
    } else {
        Serial.println("4G MQTT连接失败，将在下一次循环重试");
    }

    // 如果连接成功，尝试订阅命令主题（非阻塞，只尝试一次）
    if (result && strlen(_commandTopic) > 0) {
        // 不再检查连接状态，直接尝试订阅
        bool subscribed = subscribe4GMQTT(_commandTopic);
        if (subscribed) {
            Serial.printf("4G MQTT订阅命令主题 '%s' 成功\n", _commandTopic);
        } else {
            Serial.printf("4G MQTT订阅命令主题 '%s' 失败\n", _commandTopic);
            // 不要因为订阅失败而返回false，保持连接状态
            // 订阅将在后续的连接检查中自动重试
        }
    } else if (result) {
        Serial.println("4G MQTT命令主题为空，跳过订阅");
    } else {
        Serial.println("4G MQTT连接失败，无法订阅主题");
    }

    return result;
}

// 断开4G MQTT
bool MQTTClient::disconnect4GMQTT() {
    if (!_4GMQTTConnected) {
        return true;
    }

    bool result = _air780e->mqttDisconnect();
    if (result) {
        _4GMQTTConnected = false;
        _use4GMQTT = false;
        Serial.println("4G MQTT断开连接成功");
    } else {
        Serial.println("4G MQTT断开连接失败");
    }

    return result;
}

// 检查4G MQTT连接状态
bool MQTTClient::is4GMQTTConnected() {
    if (!_4GMQTTConfigured) {
        return false;
    }

    bool connected = _air780e->mqttIsConnected();
    if (connected != _4GMQTTConnected) {
        if (connected) {
            _4GMQTTConnected = true;
            Serial.println("4G MQTT连接成功");
        } else {
            _4GMQTTConnected = false;
            Serial.println("4G MQTT连接断开");

            // 连接断开时，标记为未配置，这样下次连接时会重新配置
            // 这解决了重连后客户端ID为空的问题
            _4GMQTTConfigured = false;
            Serial.println("4G MQTT标记为未配置，下次连接时将重新配置");
        }
    }

    return connected;
}

// 发布4G MQTT消息
bool MQTTClient::publish4GMQTT(const char* topic, const char* payload, uint8_t qos) {
    if (!_4GMQTTConnected) {
        Serial.println("4G MQTT发布失败: 未连接");
        return false;
    }

    bool result = _air780e->mqttPublish(topic, payload, qos);
    if (result) {
        Serial.printf("4G MQTT发布消息到 '%s' 成功\n", topic);
    } else {
        Serial.printf("4G MQTT发布消息到 '%s' 失败\n", topic);
    }

    return result;
}

// 订阅4G MQTT主题 - 非阻塞，立即返回结果
bool MQTTClient::subscribe4GMQTT(const char* topic) {
    // 检查连接状态
    if (!is4GMQTTConnected()) {
        Serial.println("4G MQTT订阅失败: 未连接");
        return false;
    }

    // 尝试订阅，只尝试一次，立即返回结果
    bool result = _air780e->mqttSubscribe(topic);

    // 记录结果但不重试
    if (result) {
        Serial.printf("4G MQTT订阅主题 '%s' 成功\n", topic);
    } else {
        Serial.printf("4G MQTT订阅主题 '%s' 失败\n", topic);
    }

    return result;
}

// 取消订阅4G MQTT主题
bool MQTTClient::unsubscribe4GMQTT(const char* topic) {
    if (!_4GMQTTConnected) {
        Serial.println("4G MQTT取消订阅失败: 未连接");
        return false;
    }

    bool result = _air780e->mqttUnsubscribe(topic);
    if (result) {
        Serial.printf("4G MQTT取消订阅主题 '%s' 成功\n", topic);
    } else {
        Serial.printf("4G MQTT取消订阅主题 '%s' 失败\n", topic);
    }

    return result;
}

// 处理4G MQTT事件
void MQTTClient::handle4GMQTTEvents() {
    if (!_4GMQTTConfigured) {
        return;
    }

    // 只处理消息接收，不处理连接管理
    _air780e->mqttLoop();
}
