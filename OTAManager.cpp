#include "OTAManager.h"

OTAManager::OTAManager() 
    : _isEnabled(false), 
      _isUpdating(false) {
    
    // 初始化配置
    memset(_hostname, 0, sizeof(_hostname));
    memset(_password, 0, sizeof(_password));
    
    // 设置默认主机名
    strncpy(_hostname, DEVICE_NAME, sizeof(_hostname) - 1);
}

bool OTAManager::begin() {
    // 配置ArduinoOTA
    ArduinoOTA.setHostname(_hostname);
    
    if (strlen(_password) > 0) {
        ArduinoOTA.setPassword(_password);
    }
    
    // 设置回调
    ArduinoOTA.onStart([this]() {
        _isUpdating = true;
        String type = ArduinoOTA.getCommand() == U_FLASH ? "sketch" : "filesystem";
        Serial.println("开始OTA升级: " + type);
    });
    
    ArduinoOTA.onEnd([this]() {
        _isUpdating = false;
        Serial.println("\nOTA升级完成");
    });
    
    ArduinoOTA.onProgress([](unsigned int progress, unsigned int total) {
        Serial.printf("升级进度: %u%%\r", (progress / (total / 100)));
    });
    
    ArduinoOTA.onError([this](ota_error_t error) {
        _isUpdating = false;
        Serial.printf("OTA升级错误[%u]: ", error);
        if (error == OTA_AUTH_ERROR) Serial.println("认证失败");
        else if (error == OTA_BEGIN_ERROR) Serial.println("开始失败");
        else if (error == OTA_CONNECT_ERROR) Serial.println("连接失败");
        else if (error == OTA_RECEIVE_ERROR) Serial.println("接收失败");
        else if (error == OTA_END_ERROR) Serial.println("结束失败");
    });
    
    return true;
}

bool OTAManager::start() {
    // 检查WiFi是否已连接
    if (WiFi.status() != WL_CONNECTED)
    {
        Serial.println("OTA启动失败：WiFi未连接");
        return false;
    }

    // 初始化MDNS
    if (!MDNS.begin(_hostname))
    {
        Serial.println("MDNS初始化失败");
        // 继续执行，因为OTA可能仍然可以工作
    }
    
    // 启动OTA服务(启动之前必须wifi网络连接成功)
    ArduinoOTA.begin();
    _isEnabled = true;
    Serial.println("OTA服务启动成功");
    return true;
}

bool OTAManager::stop() {
    // 停止OTA服务
    _isEnabled = false;
    return true;
}

void OTAManager::handleEvents() {
    // 处理OTA事件
    if (_isEnabled) {
        ArduinoOTA.handle();
    }
}

void OTAManager::setPassword(const char* password) {
    strncpy(_password, password, sizeof(_password) - 1);
    
    // 更新ArduinoOTA配置
    if (strlen(_password) > 0) {
        ArduinoOTA.setPassword(_password);
    }
}

void OTAManager::setHostname(const char* hostname) {
    strncpy(_hostname, hostname, sizeof(_hostname) - 1);
    
    // 更新ArduinoOTA配置
    ArduinoOTA.setHostname(_hostname);
}

bool OTAManager::isUpdating() {
    return _isUpdating;
}
