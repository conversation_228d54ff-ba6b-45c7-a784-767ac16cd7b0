#ifndef CONFIG_H
#define CONFIG_H

#include <Arduino.h>

// 系统版本信息
#define FIRMWARE_VERSION "1.0.0"
#define DEVICE_NAME "TXWRGJ"

// 蓝牙配置
#define BLE_DEVICE_NAME "TXWRGJ"
#define BLE_SERVICE_UUID        "4fafc201-1fb5-459e-8fcc-c5c9c331914b"
#define BLE_CONFIG_CHAR_UUID    "beb5483e-36e1-4688-b7f5-ea07361b26a8"
#define BLE_STATUS_CHAR_UUID    "1c95d5e3-d8f7-413a-bf3d-7a2e5d7be87e"
#define BLE_COMMAND_CHAR_UUID   "5e9bf364-f2d9-4213-90c1-28e0f6d9243a"

// 默认配置
#define DEFAULT_AP_SSID "VM-"
#define DEFAULT_AP_PASSWORD "12345678"
#define DEFAULT_MQTT_SERVER "8.149.139.39"
#define DEFAULT_MQTT_PORT 1883
#define DEFAULT_MQTT_USER "admin"
#define DEFAULT_MQTT_PASSWORD "123456@Abc"
#define DEFAULT_MQTT_TOPIC_STATUS "DRIVER"
#define DEFAULT_MQTT_CLIENT_ID "ESP32PI_MAC"
#define DEFAULT_MQTT_TOPIC_COMMAND "DRIVER"
// API接口默认地址
#define DEFAULT_API_GOODS_OUT "http://xmc.xmtxrj.com:8082/apiSaasWrgj/memberOrderGoodsOut/updateMemberOrderGoodsOutStatus_barCode"
#define DEFAULT_API_CABINET_BIN_TEST "http://xmc.xmtxrj.com:8082/apiSaasWrgj/cabinetBinTestDetail/updateCabinetcabinetBinTestStatus"
// #define DEFAULT_API_GET_DEVICE_PARAMS "http://xmc.xmtxrj.com:8082/cabinetInfo/deviceParams"

// 硬件引脚定义
// 红外传感器引脚
#define PIN_IR_SENSOR 5      // 原有红外传感器
#define PIN_PIR_SENSOR 6     // 人体红外感应器
#define PIN_PIR_SENSOR2 16   // 新增人体红外传感器1
#define PIN_PIR_SENSOR3 19   // 新增人体红外传感器2

// 继电器控制引脚
#define PIN_RELAY1 35        // 继电器1
#define PIN_RELAY2 36        // 继电器2
#define PIN_RELAY3 37        // 继电器3
#define PIN_RELAY4 38        // 继电器4
#define PIN_RELAY5 39        // 继电器5

#define PIN_RESET_BUTTON 0  // 重置按键引脚，使用ESP32的GPIO0（通常有一个板载按键）

// I2S音频接口引脚
#define I2S_BCLK_PIN 40      // I2S时钟引脚
#define I2S_LRCLK_PIN 41     // I2S帧同步引脚
#define I2S_DOUT_PIN 21      // I2S数据输出引脚
#define I2S_DIN_PIN 20       // I2S数据输入引脚

// 音频配置
#define SAMPLE_RATE 16000    // 采样率
#define BITS_PER_SAMPLE 16   // 采样位数

// RS485串口定义
#define RS485_SERIAL Serial1
#define RS485_BAUD 9600
#define PIN_RS485_TX 17
#define PIN_RS485_RX 18
#define PIN_RS485_DE 4

// 电机控制串口定义 (RS232)
#define MOTOR_SERIAL Serial1  // 与RS485共用串口，通过接口类型选择
#define MOTOR_BAUD 9600
#define PIN_MOTOR_TX 17
#define PIN_MOTOR_RX 18

// 4G模块串口定义
#define AIR780E_SERIAL Serial2
#define AIR780E_BAUD 115200
#define PIN_AIR780E_TX 14
#define PIN_AIR780E_RX 15
#define PIN_AIR780E_PWRKEY 13

// 网络模式
enum NetworkMode {
    NETWORK_MODE_WIFI = 0,
    NETWORK_MODE_4G = 1
};

// 系统配置结构
struct SystemConfig {
    // WiFi配置
    char wifi_ssid[32];
    char wifi_password[64];
    bool wifi_enabled;

    // 4G配置
    char apn[32];
    char apn_user[32];
    char apn_password[32];
    bool cellular_enabled;

    // 网络模式选择
    NetworkMode network_mode;

    // MQTT配置
    char mqtt_server[64];
    uint16_t mqtt_port;
    char mqtt_user[32];
    char mqtt_password[32];
    char mqtt_client_id[64];
    char mqtt_topic_status[64];
    char mqtt_topic_command[64];
    uint16_t mqtt_update_interval;  // 位置上报间隔(秒)

    // 设备信息
    char device_id[64];
    char device_name[32];

    // web服务配置
    char web_server_ssid[32];
    char web_server_password[64];

    // api服务器地址
    char api_goodsOut[200];
    char api_cabinetBinTest[200];
    // char api_getDeviceParams[200];
};

// 设备状态结构
struct DeviceStatus {
    bool wifi_connected;
    bool cellular_connected;
    bool mqtt_connected;
    bool motor_running;
    bool ir_triggered;        // 原有红外传感器状态
    bool ir_triggered2;       // 新增红外传感器1状态
    bool ir_triggered3;       // 新增红外传感器2状态
    bool pir_triggered;       // 人体红外感应器状态
    bool relay_status[5];     // 5个继电器状态
    float latitude;
    float longitude;
    uint32_t last_location_update;
    uint32_t last_mqtt_publish;
    uint32_t uptime;
    uint8_t signal_strength;  // 4G信号强度(0-100)
    char mac_address[17];     // 设备唯一ID(16个字符+结束符)
    bool device_communication_error; // 设备通信异常
    bool device_status;       // 设备状态（通过allowScan判断）
    uint8_t query_timeout_count; // 查询超时计数
};

#endif // CONFIG_H
