#include "DeviceControl.h"

DeviceControl::DeviceControl()
    : _irState(false),
      _irState2(false),
      _irState3(false),
      _pirState(false),
      _lastPirTriggerTime(0),
      _pirAudioIndex(AUDIO_WELCOME), // 默认播放欢迎语音
      _dispenseTimeout(10000), // 默认10秒超时
      _dispenseStartTime(0),
      _currentChannelNumber(DEFAULT_CHANNEL_NUMBER),
      _currentColumnNumber(DEFAULT_COLUMN_NUMBER),
      _anotherChannelNumber(DEFAULT_CHANNEL_NUMBER),
      _anotherColumnNumber(DEFAULT_COLUMN_NUMBER),
      _currentQuantity(1),
      _isMultiChannel(false),
      _dispensing(false),
      _isNeedDropDetection(false)
{
    // 初始化继电器状态
    for (int i = 0; i < 5; i++) {
        _relayState[i] = false; // 默认关闭
    }

    _lastDispenseResult.aisleState = 0;
    _lastDispenseResult.saleDetectionResult = false;
    _lastDispenseResult.finalShipmentResult = false;
}

bool DeviceControl::begin()
{
    // 初始化电机控制器
    _motorController.begin(INTERFACE_SZ1_RS232);

    // 初始化音频管理器
    if (!_audioManager.begin()) {
        Serial.println("音频管理器初始化失败");
        // 不返回false，允许系统继续运行
    }

    // 配置红外传感器引脚
    pinMode(PIN_IR_SENSOR, INPUT_PULLUP);
    pinMode(PIN_PIR_SENSOR2, INPUT_PULLUP);
    pinMode(PIN_PIR_SENSOR3, INPUT_PULLUP);
    pinMode(PIN_PIR_SENSOR, INPUT); // 人体红外感应器通常是高电平触发

    // 读取初始红外状态
    _irState = digitalRead(PIN_IR_SENSOR) == LOW;
    _irState2 = digitalRead(PIN_PIR_SENSOR2) == LOW;
    _irState3 = digitalRead(PIN_PIR_SENSOR3) == LOW;
    _pirState = digitalRead(PIN_PIR_SENSOR) == HIGH; // 人体红外感应器通常是高电平触发

    Serial.printf("初始红外传感器状态: IR1=%d, IR2=%d, IR3=%d, PIR=%d\n",
                 _irState ? 1 : 0,
                 _irState2 ? 1 : 0,
                 _irState3 ? 1 : 0,
                 _pirState ? 1 : 0);

    // 配置继电器引脚
    pinMode(PIN_RELAY1, OUTPUT);
    pinMode(PIN_RELAY2, OUTPUT);
    pinMode(PIN_RELAY3, OUTPUT);
    pinMode(PIN_RELAY4, OUTPUT);
    pinMode(PIN_RELAY5, OUTPUT);

    // 初始化继电器状态（默认关闭）
    digitalWrite(PIN_RELAY1, LOW);
    digitalWrite(PIN_RELAY2, LOW);
    digitalWrite(PIN_RELAY3, LOW);
    digitalWrite(PIN_RELAY4, LOW);
    digitalWrite(PIN_RELAY5, LOW);

    Serial.println("继电器初始化完成，默认状态为关闭");

    // 初始化重置按键
    pinMode(PIN_RESET_BUTTON, INPUT_PULLUP);

    // 初始化音频管理器
    // bool audioResult = _audioManager.begin();
    // if (audioResult) {
    //     Serial.println("音频管理器初始化成功");
    //     // 设置适当的音量
    //     _audioManager.setVolume(80);
    // } else {
    //     Serial.println("音频管理器初始化失败");
    // }

    return true;
}

bool DeviceControl::checkIRSensor()
{
    // 读取原有红外传感器状态
    bool currentState = digitalRead(PIN_IR_SENSOR) == LOW;

    // 检测状态变化
    bool stateChanged = (_irState != currentState);
    if (stateChanged) {
        Serial.printf("红外传感器1状态变化: %s\n", currentState ? "触发" : "未触发");
    }
    _irState = currentState;

    return _irState;
}

bool DeviceControl::checkIRSensor2()
{
    // 读取新增红外传感器1状态
    bool currentState = digitalRead(PIN_PIR_SENSOR2) == LOW;

    // 检测状态变化
    bool stateChanged = (_irState2 != currentState);
    if (stateChanged) {
        Serial.printf("红外传感器2状态变化: %s\n", currentState ? "触发" : "未触发");
    }
    _irState2 = currentState;

    return _irState2;
}

bool DeviceControl::checkIRSensor3()
{
    // 读取新增红外传感器2状态
    bool currentState = digitalRead(PIN_PIR_SENSOR3) == LOW;

    // 检测状态变化
    bool stateChanged = (_irState3 != currentState);
    if (stateChanged) {
        Serial.printf("红外传感器3状态变化: %s\n", currentState ? "触发" : "未触发");
    }
    _irState3 = currentState;

    return _irState3;
}

bool DeviceControl::checkPIRSensor()
{
    // 读取人体红外感应器状态
    bool currentState = digitalRead(PIN_PIR_SENSOR) == HIGH; // 高电平表示检测到人体

    // 检测状态变化
    bool stateChanged = (_pirState != currentState);
    if (stateChanged) {
        Serial.printf("人体红外感应器状态变化: %s\n", currentState ? "检测到人体" : "未检测到人体");

        // 如果检测到人体，且距离上次触发超过10秒，播放指定音频
        if (currentState && (millis() - _lastPirTriggerTime > 10000)) {
            _lastPirTriggerTime = millis();
            Serial.printf("人体红外感应器触发，播放音频索引: %d\n", _pirAudioIndex);
            playAudio(_pirAudioIndex);
        }
    }

    _pirState = currentState;
    return _pirState;
}

bool DeviceControl::playWelcomeVoice()
{
    Serial.println("播放欢迎语音");
    return _audioManager.playWelcome();
}

bool DeviceControl::playAudio(uint8_t index)
{
    Serial.printf("播放音频: 索引=%d\n", index);
    return _audioManager.playAudio(index);
}

bool DeviceControl::stopAudio()
{
    Serial.println("停止播放音频");
    return _audioManager.stopPlay();
}

bool DeviceControl::setAudioVolume(uint8_t volume)
{
    Serial.printf("设置音频音量: %d%%\n", volume);
    return _audioManager.setVolume(volume);
}

uint8_t DeviceControl::getAudioVolume()
{
    return _audioManager.getVolume();
}

bool DeviceControl::saveAudioData(uint8_t index, const uint8_t* data, size_t size)
{
    Serial.printf("警告: saveAudioData已弃用，请使用saveAudioURL\n");
    return false;
}

bool DeviceControl::saveAudioURL(uint8_t index, const String& url)
{
    Serial.printf("保存音频URL: 索引=%d, URL=%s\n", index, url.c_str());
    return _audioManager.saveAudioURL(index, url);
}

bool DeviceControl::downloadAudioFile(uint8_t index, const String& url)
{
    Serial.printf("下载音频文件: 索引=%d, URL=%s\n", index, url.c_str());
    return _audioManager.downloadAudioFile(index, url);
}

bool DeviceControl::deleteLocalAudio(uint8_t index)
{
    Serial.printf("删除本地音频文件: 索引=%d\n", index);
    return _audioManager.deleteLocalAudio(index);
}

bool DeviceControl::localAudioExists(uint8_t index)
{
    return _audioManager.localAudioExists(index);
}

bool DeviceControl::dispenseProduct(uint32_t channelNumber, uint32_t ColumnNumber)
{
    // 重置出货结果
    _lastDispenseResult.aisleState = 0;
    _lastDispenseResult.saleDetectionResult = false;
    _lastDispenseResult.finalShipmentResult = false;
    _currentChannelNumber = channelNumber;
    _currentColumnNumber = ColumnNumber;
    _isMultiChannel = false;
    _dispensing = true;
    _dispenseStartTime = millis();

    // 发送单通道出货命令
    bool result = _motorController.dispenseSingleChannel(channelNumber, ColumnNumber);

    if (!result) {
        _dispensing = false;
        return false;
    }

    return true;
}

bool DeviceControl::dispenseMultiProduct(uint16_t quantity, uint16_t channelNumber, uint32_t ColumnNumber, uint16_t anotherChannelNumber, uint32_t anothercolumnNumber)
{
    // 重置出货结果
    _lastDispenseResult.aisleState = 0;
    _lastDispenseResult.saleDetectionResult = false;
    _lastDispenseResult.finalShipmentResult = false;
    _currentChannelNumber = channelNumber;
    _currentColumnNumber = ColumnNumber;
    _anotherChannelNumber = anotherChannelNumber;
    _anotherColumnNumber = anothercolumnNumber;
    _currentQuantity = quantity;
    _isMultiChannel = true;
    _dispensing = true;
    _dispenseStartTime = millis();

    // 发送多通道出货命令
    bool result = _motorController.dispenseMultiChannel(channelNumber, ColumnNumber, anotherChannelNumber, anothercolumnNumber, quantity);

    if (!result) {
        _dispensing = false;
        return false;
    }

    return true;
}

DeliveryResultData DeviceControl::getLastDispenseResult()
{
    return _lastDispenseResult;
}

void DeviceControl::setDispenseTimeout(uint32_t timeout)
{
    _dispenseTimeout = timeout;
}

void DeviceControl::setNeedDropDetection(bool need)
{
    _isNeedDropDetection = need;
    Serial.printf("设置红外检测状态: %s\n", need ? "启用" : "禁用");
}

bool DeviceControl::isNeedDropDetection()
{
    return _isNeedDropDetection;
}

void DeviceControl::handleEvents()
{
    // 处理电机控制器事件
    _motorController.handleEvents();

    // 处理音频事件
    _audioManager.handleEvents();

    // 检查人体红外感应器状态
    checkPIRSensor();

    // 检查出货状态
    if (_dispensing) {
        // 获取出货状态
        uint8_t dispenseStatus = _motorController.getLastDispenseStatus();
        unsigned long currentTime = millis();

        // 如果出货完成或失败
        if (dispenseStatus == DISPENSE_STATUS_COMPLETE || dispenseStatus == DISPENSE_STATUS_FAILED) {
            // 检查所有红外传感器
            bool irTriggered = checkIRSensor();
            _lastDispenseResult.saleDetectionResult = irTriggered;
            // 如果出货完成且红外被触发，则出货成功
            if (dispenseStatus == DISPENSE_STATUS_COMPLETE)
            {
                _lastDispenseResult.aisleState = 1;
                if (_isNeedDropDetection) {
                    while (!irTriggered && (currentTime - _dispenseStartTime < 6000)) {
                        // 等待红外检测到物品掉落
                        delay(10);
                        currentTime = millis();
                        irTriggered = checkIRSensor();
                        _lastDispenseResult.saleDetectionResult = irTriggered;
                    }
                    _lastDispenseResult.finalShipmentResult = irTriggered;
                } else {
                    _lastDispenseResult.finalShipmentResult = true;
                }
            }
            else
            {
                _lastDispenseResult.aisleState = 0;
                _lastDispenseResult.finalShipmentResult = false;
            }

            _dispensing = false;
            Serial.printf("出货完成，状态=%d, 结果=%d\n", dispenseStatus, _lastDispenseResult.finalShipmentResult);
        }
        // 如果出货状态为进行中
        else if (dispenseStatus == DISPENSE_STATUS_IN_PROGRESS) {
            // 每3秒打印一次状态
            static unsigned long lastStatusTime = 0;
            if (currentTime - lastStatusTime > 5000) {
                lastStatusTime = currentTime;
                Serial.println("出货状态: 进行中...");

                // 检查所有红外传感器
                bool irTriggered = checkIRSensor();

                // 打印所有红外传感器状态
                Serial.printf("红外传感器状态: IR1=%s",
                             irTriggered ? "已触发" : "未触发");

                // 计算已经过去的时间
                unsigned long elapsedTime = currentTime - _dispenseStartTime;
                Serial.printf("已经过去 %lu 毫秒，超时时间 %lu 毫秒\n", elapsedTime, _dispenseTimeout);
            }
        }
        // 检查是否超时
        else if (currentTime - _dispenseStartTime > _dispenseTimeout) {
            _dispensing = false;
            _lastDispenseResult.aisleState = 0;
            _lastDispenseResult.saleDetectionResult = checkIRSensor();
            _lastDispenseResult.finalShipmentResult = false;
            Serial.println("出货超时，结果=失败");
        }
    }
}

void DeviceControl::setSerialInterfaceType(SerialInterfaceType type)
{
    _motorController.setInterfaceType(type);
}

bool DeviceControl::queryDeviceStatus(bool isNetworkConnected)
{
    return _motorController.queryDevice(isNetworkConnected);
}

bool DeviceControl::getDeviceStatus()
{
    // 通过allowScan判断设备状态
    return _motorController.getAllowScanStatus();
}

uint8_t DeviceControl::getDispenseStatus()
{
    // 获取当前出货状态
    return _motorController.getLastDispenseStatus();
}

bool DeviceControl::isDispensing()
{
    // 检查是否正在出货中
    return _dispensing;
}

bool DeviceControl::isDeviceCommunicationError()
{
    // 获取设备通信状态（是否异常）
    return _motorController.isDeviceCommunicationError();
}

// 获取通信接口类型
SerialInterfaceType DeviceControl::getInterfaceType()
{
    return _motorController.getInterfaceType();
}

bool DeviceControl::setRelay(uint8_t relayNumber, bool state)
{
    // 检查继电器编号是否有效
    if (relayNumber < 1 || relayNumber > 5) {
        Serial.printf("错误: 无效的继电器编号 %d\n", relayNumber);
        return false;
    }

    // 获取对应的继电器引脚
    uint8_t relayPin;
    switch (relayNumber) {
        case 1: relayPin = PIN_RELAY1; break;
        case 2: relayPin = PIN_RELAY2; break;
        case 3: relayPin = PIN_RELAY3; break;
        case 4: relayPin = PIN_RELAY4; break;
        case 5: relayPin = PIN_RELAY5; break;
        default: return false;
    }

    // 设置继电器状态
    digitalWrite(relayPin, state ? HIGH : LOW);
    _relayState[relayNumber - 1] = state;

    Serial.printf("继电器 %d 设置为 %s\n", relayNumber, state ? "开启" : "关闭");

    return true;
}

bool DeviceControl::getRelayStatus(uint8_t relayNumber)
{
    // 检查继电器编号是否有效
    if (relayNumber < 1 || relayNumber > 5) {
        Serial.printf("错误: 无效的继电器编号 %d\n", relayNumber);
        return false;
    }

    return _relayState[relayNumber - 1];
}

void DeviceControl::getAllRelayStatus(bool relayStatus[5])
{
    for (int i = 0; i < 5; i++) {
        relayStatus[i] = _relayState[i];
    }
}

bool DeviceControl::setAllRelays(bool state)
{
    // 设置所有继电器状态
    digitalWrite(PIN_RELAY1, state ? HIGH : LOW);
    digitalWrite(PIN_RELAY2, state ? HIGH : LOW);
    digitalWrite(PIN_RELAY3, state ? HIGH : LOW);
    digitalWrite(PIN_RELAY4, state ? HIGH : LOW);
    digitalWrite(PIN_RELAY5, state ? HIGH : LOW);

    // 更新状态数组
    for (int i = 0; i < 5; i++) {
        _relayState[i] = state;
    }

    Serial.printf("所有继电器设置为 %s\n", state ? "开启" : "关闭");

    return true;
}

void DeviceControl::setPIRAudioIndex(uint8_t audioIndex)
{
    // 验证音频索引范围
    if (audioIndex >= 3) {  // AUDIO_COUNT = 3
        Serial.printf("警告: 音频索引超出范围 (0-2)，使用默认值\n");
        _pirAudioIndex = AUDIO_WELCOME;
    } else {
        _pirAudioIndex = audioIndex;
        Serial.printf("设置人体红外感应器音频索引: %d\n", _pirAudioIndex);
    }
}

uint8_t DeviceControl::getPIRAudioIndex() const
{
    return _pirAudioIndex;
}
