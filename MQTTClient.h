#ifndef MQTT_CLIENT_H
#define MQTT_CLIENT_H

#include <Arduino.h>
#include <PubSubClient.h>
#include <WiFiClient.h>
#include <functional>
#include "config.h"
#include "HardwareNetworkManager.h"
#include "Air780E.h"

// 定义回调函数类型
typedef std::function<void(String topic, String message)> MQTTCallback;

class MQTTClient {
public:
    MQTTClient(HardwareNetworkManager* networkManager, Air780E* air780e);

    // 初始化MQTT客户端
    bool begin();

    // 配置MQTT服务器
    bool configure(const char* server, uint16_t port, const char* username, const char* password, const char* clientId);

    // 设置主题
    void setTopics(const char* statusTopic, const char* commandTopic);

    // 连接到MQTT服务器
    bool connect();

    // 断开MQTT连接
    void disconnect();

    // 检查MQTT连接状态
    bool isConnected();

    // 发布消息
    bool publish(const char* topic, const char* payload, uint8_t qos = 1);

    // 订阅主题
    bool subscribe(const char* topic);

    // 取消订阅主题
    bool unsubscribe(const char* topic);

    // 设置消息回调
    void setCallback(MQTTCallback callback);

    // 处理MQTT事件
    void handleEvents();

    // 发布状态信息
    bool publishStatus(const char* topicCode, const DeviceStatus& status);

    // 发布位置信息（发布到状态主题）
    bool publishLocation(const char* topicCode, float latitude, float longitude);

    // 发布参数信息（发布到状态主题）
    bool publishParams(const char* topicCode, const SystemConfig& config);

    // 发布_statusTopic主题的函数
    bool publishStatusTopic(String payload, uint8_t qos = 0);

private:
    HardwareNetworkManager* _hardwareNetworkManager;
    Air780E* _air780e;
    WiFiClient _wifiClient;
    PubSubClient _mqttClient;

    // MQTT配置
    char _server[64];
    uint16_t _port;
    char _username[32];
    char _password[32];
    char _clientId[64];

    // MQTT主题
    char _statusTopic[64];
    char _commandTopic[64];

    // 回调函数
    MQTTCallback _callback;

    // 内部回调处理
    static void _mqttCallback(char *topic, uint8_t *payload, unsigned int length);

    // 4G MQTT回调处理
    static void _4gMqttCallback(const char* topic, const char* payload);

    // 静态实例指针(用于回调)
    static MQTTClient* _instance;

    // 保存最后的MQTT连接状态
    bool _lastConnected;
    unsigned long _lastConnectedTime;

    // 4G MQTT相关
    bool _use4GMQTT;
    bool _4GMQTTConfigured;
    bool _4GMQTTConnected;

    // 配置4G MQTT
    bool configure4GMQTT();

    // 连接4G MQTT
    bool connect4GMQTT();

    // 断开4G MQTT
    bool disconnect4GMQTT();

    // 检查4G MQTT连接状态
    bool is4GMQTTConnected();

    // 发布4G MQTT消息
    bool publish4GMQTT(const char* topic, const char* payload, uint8_t qos = 1);

    // 订阅4G MQTT主题
    bool subscribe4GMQTT(const char* topic);

    // 取消订阅4G MQTT主题
    bool unsubscribe4GMQTT(const char* topic);

    // 处理4G MQTT事件
    void handle4GMQTTEvents();
};

#endif // MQTT_CLIENT_H
