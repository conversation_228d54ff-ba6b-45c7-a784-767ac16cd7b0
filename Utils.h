#ifndef UTILS_H
#define UTILS_H

#include <Arduino.h>

class Utils {
public:
    // 格式化时间(秒)为可读字符串
    static String formatTime(uint32_t seconds);
    
    // 格式化字节数为可读字符串
    static String formatBytes(size_t bytes);
    
    // 生成随机字符串
    static String randomString(uint8_t length);
    
    // 解析MAC地址
    static String formatMAC(const uint8_t* mac);
    
    // 检查字符串是否为有效IP地址
    static bool isValidIP(const char* ip);
    
    // 检查字符串是否为有效域名
    static bool isValidDomain(const char* domain);
    
    // 从字符串中提取数字
    static int extractNumber(const String& str);
};

#endif // UTILS_H
