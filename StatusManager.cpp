#include "StatusManager.h"
#include "MQTTClient.h"
#include "DeviceControl.h"
#include "Air780E.h"
#include "HardwareNetworkManager.h"
#include "ConnectionManager.h"
#include "WebServerManager.h"
#include "SystemCore.h"

// 声明外部SystemCore对象
extern SystemCore systemCore;

StatusManager::StatusManager()
    : _mqttClient(nullptr),
      _deviceControl(nullptr),
      _air780e(nullptr),
      _hardwareNetworkManager(nullptr),
      _connectionManager(nullptr),
      _webServer(nullptr),
      _lastLocationUpdate(0),
      _lastStatusPublish(0) {

    // 初始化设备状态
    memset(&_deviceStatus, 0, sizeof(_deviceStatus));
}

bool StatusManager::begin(MQTTClient* mqttClient, DeviceControl* deviceControl, Air780E* air780e,
                         HardwareNetworkManager* networkManager, ConnectionManager* connectionManager,
                         WebServerManager* webServer) {
    _mqttClient = mqttClient;
    _deviceControl = deviceControl;
    _air780e = air780e;
    _hardwareNetworkManager = networkManager;
    _connectionManager = connectionManager;
    _webServer = webServer;

    // 初始化时更新一次设备状态
    updateDeviceStatus();

    return true;
}

void StatusManager::updateDeviceStatus() {
    // 更新网络状态
    _deviceStatus.wifi_connected = (WiFi.status() == WL_CONNECTED);
    _deviceStatus.cellular_connected = _air780e->isConnected();

    // 更新MQTT状态
    _deviceStatus.mqtt_connected = _connectionManager->isMqttConnected();

    // 更新设备状态
    _deviceStatus.motor_running = _deviceControl->isDispensing();  // 是否正在出货

    // 更新所有红外传感器状态
    _deviceStatus.ir_triggered = _deviceControl->checkIRSensor();
    _deviceStatus.ir_triggered2 = _deviceControl->checkIRSensor2();
    _deviceStatus.ir_triggered3 = _deviceControl->checkIRSensor3();
    _deviceStatus.pir_triggered = _deviceControl->checkPIRSensor();

    // 更新所有继电器状态
    _deviceControl->getAllRelayStatus(_deviceStatus.relay_status);

    // 更新设备通信状态和设备状态
    _deviceStatus.device_communication_error = _deviceControl->isDeviceCommunicationError();
    _deviceStatus.device_status = _deviceControl->getDeviceStatus();

    // 更新查询超时计数（这里假设为0，实际应该从SerialMotorController获取）
    _deviceStatus.query_timeout_count = 0; // 这里需要添加获取查询超时计数的方法

    // 更新信号强度
    _deviceStatus.signal_strength = _hardwareNetworkManager->getSignalStrength();

    // 更新运行时间
    _deviceStatus.uptime = millis() / 1000;

    // 更新最后MQTT发布时间
    _deviceStatus.last_mqtt_publish = _lastStatusPublish;

    // 获取并更新设备唯一ID
    String deviceId = _hardwareNetworkManager->getUniqueDeviceId();
    strncpy(_deviceStatus.mac_address, deviceId.c_str(), sizeof(_deviceStatus.mac_address) - 1);
    _deviceStatus.mac_address[sizeof(_deviceStatus.mac_address) - 1] = '\0'; // 确保字符串结束

    // 获取并记录当前时间
    String currentTime = systemCore.getCurrentTimeString();
    Serial.print("状态更新时间: ");
    Serial.println(currentTime);

    // 更新Web服务器状态
    _webServer->updateStatus(_deviceStatus);
}

void StatusManager::handleEvents() {
    unsigned long currentMillis = millis();

    // 定时发布状态
    // if (currentMillis - _lastStatusPublish > STATUS_UPDATE_INTERVAL) {
    //     _lastStatusPublish = currentMillis;

    //     // 发布状态
    //     if (_connectionManager->isMqttConnected()) {
    //         publishStatus("get_status");
    //     }
    // }

    // 定时更新位置
    // if (currentMillis - _lastLocationUpdate > LOCATION_UPDATE_INTERVAL) {
    //     _lastLocationUpdate = currentMillis;

    //     // 更新位置
    //     if (updateLocation() && _connectionManager->isMqttConnected()) {
    //         // 发布位置
    //         publishLocation("getGpsInfo");
    //     }
    // }
}

bool StatusManager::publishParams(const char* topicCode) {
    // 发布参数
    Serial.println("发布设备参数...");
    bool result = _mqttClient->publishParams(topicCode, _webServer->getConfig());
    Serial.printf("发布参数%s\n", result ? "成功" : "失败");

    return result;
}

bool StatusManager::publishStatus(const char* topicCode) {
    // 确保状态是最新的
    updateDeviceStatus();

    // 发布状态
    Serial.println("发布设备状态...");
    bool result = _mqttClient->publishStatus(topicCode, _deviceStatus);
    Serial.printf("发布状态%s\n", result ? "成功" : "失败");

    return result;
}

bool StatusManager::publishLocation(const char* topicCode) {
    // 发布位置
    Serial.println("发布设备位置...");
    bool result = _mqttClient->publishLocation(topicCode, _deviceStatus.latitude, _deviceStatus.longitude);
    Serial.printf("发布位置%s\n", result ? "成功" : "失败");

    return result;
}

bool StatusManager::updateLocation() {
    // 检查GPS是否开启
    if (!_air780e->isGpsOpen()) {
        Serial.println("GPS未开启，尝试开启GPS");
        _air780e->gpsOpen();
        // 开启GPS后需要等待一段时间才能定位
        return false;
    }

    // 获取GPS状态
    int gpsStatus = _air780e->getGpsStatus();
    Serial.printf("GPS状态: %d\n", gpsStatus);

    // 获取位置
    float latitude, longitude;
    if (gpsStatus == 1 && _air780e->getLocation(latitude, longitude)) {
        // 更新状态
        _deviceStatus.latitude = latitude;
        _deviceStatus.longitude = longitude;
        _deviceStatus.last_location_update = millis() / 1000;

        return true;
    }

    Serial.println("获取位置失败");
    return false;
}
