#include "Utils.h"

String Utils::formatTime(uint32_t seconds) {
    uint32_t days = seconds / 86400;
    seconds %= 86400;
    uint8_t hours = seconds / 3600;
    seconds %= 3600;
    uint8_t minutes = seconds / 60;
    seconds %= 60;
    
    char buffer[20];
    if (days > 0) {
        sprintf(buffer, "%lud %02uh %02um %02us", days, hours, minutes, seconds);
    } else if (hours > 0) {
        sprintf(buffer, "%02uh %02um %02us", hours, minutes, seconds);
    } else if (minutes > 0) {
        sprintf(buffer, "%02um %02us", minutes, seconds);
    } else {
        sprintf(buffer, "%02us", seconds);
    }
    
    return String(buffer);
}

String Utils::formatBytes(size_t bytes) {
    if (bytes < 1024) {
        return String(bytes) + " B";
    } else if (bytes < (1024 * 1024)) {
        return String(bytes / 1024.0, 2) + " KB";
    } else if (bytes < (1024 * 1024 * 1024)) {
        return String(bytes / 1024.0 / 1024.0, 2) + " MB";
    } else {
        return String(bytes / 1024.0 / 1024.0 / 1024.0, 2) + " GB";
    }
}

String Utils::randomString(uint8_t length) {
    static const char charset[] = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    String result = "";
    
    for (uint8_t i = 0; i < length; i++) {
        int index = random(0, sizeof(charset) - 1);
        result += charset[index];
    }
    
    return result;
}

String Utils::formatMAC(const uint8_t* mac) {
    char buffer[18];
    sprintf(buffer, "%02X:%02X:%02X:%02X:%02X:%02X", mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);
    return String(buffer);
}

bool Utils::isValidIP(const char* ip) {
    // 简单检查IP地址格式
    IPAddress addr;
    return addr.fromString(ip);
}

bool Utils::isValidDomain(const char* domain) {
    // 简单检查域名格式
    if (strlen(domain) == 0 || strlen(domain) > 253) {
        return false;
    }
    
    // 检查每个标签
    const char* p = domain;
    const char* end = domain + strlen(domain);
    
    while (p < end) {
        // 标签长度
        const char* labelEnd = strchr(p, '.');
        if (labelEnd == NULL) {
            labelEnd = end;
        }
        
        int labelLength = labelEnd - p;
        
        // 标签长度必须在1-63之间
        if (labelLength < 1 || labelLength > 63) {
            return false;
        }
        
        // 标签必须以字母或数字开头和结尾
        if (!isalnum(p[0]) || !isalnum(labelEnd[-1])) {
            return false;
        }
        
        // 标签只能包含字母、数字和连字符
        for (int i = 0; i < labelLength; i++) {
            if (!isalnum(p[i]) && p[i] != '-') {
                return false;
            }
        }
        
        // 移动到下一个标签
        p = labelEnd;
        if (p < end) {
            p++; // 跳过点
        }
    }
    
    return true;
}

int Utils::extractNumber(const String& str) {
    String numStr = "";
    
    for (unsigned int i = 0; i < str.length(); i++) {
        if (isdigit(str.charAt(i))) {
            numStr += str.charAt(i);
        }
    }
    
    if (numStr.length() > 0) {
        return numStr.toInt();
    }
    
    return 0;
}
