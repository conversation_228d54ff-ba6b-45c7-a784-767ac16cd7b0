#ifndef BLUETOOTH_MANAGER_H
#define BLUETOOTH_MANAGER_H

#include <Arduino.h>
#include <BLEDevice.h>
#include <BLEServer.h>
#include <BLEUtils.h>
#include <BLE2902.h>
#include "config.h"

// 前向声明
class SystemConfig;
class DeviceStatus;
class DeviceControl;
class StatusManager;

// 蓝牙回调类
class BluetoothCallbacks : public BLECharacteristicCallbacks {
public:
    BluetoothCallbacks(class BluetoothManager* manager);
    void onWrite(BLECharacteristic *pCharacteristic);
    void onRead(BLECharacteristic *pCharacteristic);

private:
    class BluetoothManager* _manager;
};

// 蓝牙服务器回调类
class BluetoothServerCallbacks : public BLEServerCallbacks {
public:
    BluetoothServerCallbacks(class BluetoothManager* manager);
    void onConnect(BLEServer* pServer);
    void onDisconnect(BLEServer* pServer);

private:
    class BluetoothManager* _manager;
};

class BluetoothManager {
public:
    BluetoothManager();

    // 初始化蓝牙
    bool begin(SystemConfig* config, DeviceStatus* status, DeviceControl* deviceControl, StatusManager* statusManager);

    // 处理事件
    void handleEvents();

    // 更新状态特征值
    void updateStatusCharacteristic();

    // 处理配置命令
    void handleConfigCommand(const String& command);

    // 处理控制命令
    void handleControlCommand(const String& command);

    // 获取连接状态
    bool isConnected() const;

    // 设置连接状态
    void setConnected(bool connected);

    // 获取配置
    SystemConfig* getConfig() const;

    // 获取状态
    DeviceStatus* getStatus() const;

    // 获取设备控制
    DeviceControl* getDeviceControl() const;

    // 获取状态管理器
    StatusManager* getStatusManager() const;

    // 创建JSON格式的配置字符串
    String createConfigJson();

    // 创建JSON格式的状态字符串
    String createStatusJson();

private:
    BLEServer* _server;
    BLEService* _service;
    BLECharacteristic* _configCharacteristic;
    BLECharacteristic* _statusCharacteristic;
    BLECharacteristic* _commandCharacteristic;
    BluetoothCallbacks* _callbacks;
    BluetoothServerCallbacks* _serverCallbacks;
    bool _connected;
    bool _initialized;
    unsigned long _lastStatusUpdate;
    SystemConfig* _config;
    DeviceStatus* _status;
    DeviceControl* _deviceControl;
    StatusManager* _statusManager;

    // 解析JSON格式的配置字符串
    bool parseConfigJson(const String& json);
};

#endif // BLUETOOTH_MANAGER_H
