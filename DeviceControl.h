#ifndef DEVICE_CONTROL_H
#define DEVICE_CONTROL_H

#include <Arduino.h>
#include "config.h"
#include "SerialMotorController.h"
#include "AudioManager.h"

// 出货通道默认地址
#define DEFAULT_CHANNEL_NUMBER 1
#define DEFAULT_COLUMN_NUMBER 1

// 出货状态
#define DISPENSE_STATUS_COMPLETE 0x00
#define DISPENSE_STATUS_IN_PROGRESS 0x01
#define DISPENSE_STATUS_FAILED 0x02

/**
  柜机广播返回的原始参数
*/
struct DeliveryResultData
{
    bool saleDetectionResult;   // 红外检测出货结果
    int aisleState;             // 电机转动出货结果，1为成功，0为失败
    bool finalShipmentResult;   // 最终出货结果，1为成功，0为失败
};

class DeviceControl {
public:
    DeviceControl();

    // 初始化设备控制
    bool begin();

    // 检查原有红外传感器状态
    bool checkIRSensor();

    // 检查人体红外传感器2状态
    bool checkIRSensor2();

    // 检查人体红外传感器3状态
    bool checkIRSensor3();

    // 检查人体红外感应器状态
    bool checkPIRSensor();

    // 播放欢迎语音
    bool playWelcomeVoice();

    // 播放指定索引的音频
    bool playAudio(uint8_t index);

    // 停止播放音频
    bool stopAudio();

    // 设置音频音量
    bool setAudioVolume(uint8_t volume);

    // 获取音频音量
    uint8_t getAudioVolume();

    // 保存音频数据到NVS（已弃用，保留兼容性）
    bool saveAudioData(uint8_t index, const uint8_t* data, size_t size);

    // 保存音频URL到NVS（新方法）
    bool saveAudioURL(uint8_t index, const String& url);

    // 下载音频文件到本地存储
    bool downloadAudioFile(uint8_t index, const String& url);

    // 删除本地音频文件
    bool deleteLocalAudio(uint8_t index);

    // 检查本地音频文件是否存在
    bool localAudioExists(uint8_t index);

    // 设置人体红外感应器触发时播放的音频索引
    void setPIRAudioIndex(uint8_t audioIndex);

    // 获取人体红外感应器触发时播放的音频索引
    uint8_t getPIRAudioIndex() const;

    // 出货操作
    bool dispenseProduct(uint32_t channelNumber, uint32_t ColumnNumber = 0);

    // 多通道出货操作
    bool dispenseMultiProduct(uint16_t quantity, uint16_t channelNumber, uint32_t ColumnNumber = 0, uint16_t anotherChannelNumber = 0, uint32_t anothercolumnNumber = 0);

    // 获取最后一次出货结果
    DeliveryResultData getLastDispenseResult();

    // 设置出货超时时间(毫秒)
    void setDispenseTimeout(uint32_t timeout);

    // 设置是否需要检测红外判断物品掉落
    void setNeedDropDetection(bool need);

    // 获取是否需要检测红外判断物品掉落
    bool isNeedDropDetection();

    // 处理设备事件
    void handleEvents();

    // 设置串口通信接口类型
    void setSerialInterfaceType(SerialInterfaceType type);

    // 查询设备状态
    bool queryDeviceStatus(bool isNetworkConnected);

    // 获取设备状态（通过allowScan判断）
    bool getDeviceStatus();

    // 获取当前出货状态
    uint8_t getDispenseStatus();

    // 检查是否正在出货中
    bool isDispensing();

    // 获取设备通信状态（是否异常）
    bool isDeviceCommunicationError();

    // 获取通信接口类型
    SerialInterfaceType getInterfaceType();

    // 继电器控制方法
    // 设置单个继电器状态
    bool setRelay(uint8_t relayNumber, bool state);

    // 获取单个继电器状态
    bool getRelayStatus(uint8_t relayNumber);

    // 获取所有继电器状态
    void getAllRelayStatus(bool relayStatus[5]);

    // 设置所有继电器状态
    bool setAllRelays(bool state);

private:
    SerialMotorController _motorController;
    AudioManager _audioManager;
    bool _irState;            // 原有红外传感器状态
    bool _irState2;           // 新增红外传感器1状态
    bool _irState3;           // 新增红外传感器2状态
    bool _pirState;           // 人体红外感应器状态
    bool _relayState[5];      // 5个继电器状态
    unsigned long _lastPirTriggerTime; // 上次人体红外感应器触发时间
    uint8_t _pirAudioIndex;   // 人体红外感应器触发时播放的音频索引
    DeliveryResultData _lastDispenseResult;
    uint32_t _dispenseTimeout;
    uint32_t _dispenseStartTime;
    uint32_t _currentChannelNumber;
    uint32_t _currentColumnNumber;
    uint32_t _anotherChannelNumber;
    uint32_t _anotherColumnNumber;
    uint16_t _currentQuantity;
    bool _isMultiChannel;
    bool _dispensing;
    bool _isNeedDropDetection;  // 是否需要检测红外判断物品掉落
};

#endif // DEVICE_CONTROL_H
